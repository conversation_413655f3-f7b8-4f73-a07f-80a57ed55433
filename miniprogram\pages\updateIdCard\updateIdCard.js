Page({
  data: {
    businessLicense: '',  // 营业执照图片临时路径
    idCard: ''           // 身份证图片临时路径
  },

  // 选择营业执照
  chooseBusinessLicense: function() {
    const that = this;
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function(res) {
        that.setData({
          businessLicense: res.tempFilePaths[0]
        });
      }
    });
  },

  // 选择身份证
  chooseIDCard: function() {
    const that = this;
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function(res) {
        that.setData({
          idCard: res.tempFilePaths[0]
        });
      }
    });
  },

  // 删除营业执照
  deleteBusinessLicense: function(e) {
    this.setData({
      businessLicense: ''
    });
  },

  // 删除身份证
  deleteIDCard: function(e) {
    this.setData({
      idCard: ''
    });
  },

  // 提交表单
  submitForm: function() {
    const that = this;
    
    if (!this.data.idCard) {
      wx.showToast({
        title: '请上传身份证',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '上传中...',
    });
    
    // 模拟网络请求
    setTimeout(function() {
      wx.hideLoading();
      wx.showToast({
        title: '上传成功',
        icon: 'success'
      });
      
      // 返回上一页
      setTimeout(function() {
        wx.navigateBack();
      }, 1500);
    }, 2000);
  }
});