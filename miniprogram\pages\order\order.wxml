<!--pages/order/order.wxml-->
<view class="box">
  <view class="info-container">
    <!-- 第一行：姓名和电话 -->
    <view class="info-line">
      <text class="name">{{info.name}}</text>
      <text class="phone">{{info.phone}}</text>
    </view>

    <!-- 第二行：地址 -->
    <view class="address-line">
      <text>{{info.address}}</text>
    </view>
  </view>
</view>
<view class="box">
  <view class="chose-pay-way" bind:tap="goToCreditList" wx:if="{{!creditInfo}}">选择收款方式</view>
  <view bind:tap="goToCreditList" wx:if="{{creditInfo}}">
    <view wx:if="{{creditInfo.accountType === 'bank'}}" class="credit-card">
      <view class="credit-text">账号类型：银行卡</view>
      <view class="credit-text">银行名称：{{creditInfo.bankName}}</view>
      <view class="credit-text">收款户名：{{creditInfo.accountNumber}}</view>
      <view class="credit-text">收款账号：{{creditInfo.accountNumber}}</view>
    </view>
    <view wx:if="{{creditInfo.accountType === 'wechat'}}" class="credit-card">
      <view class="credit-text">账号类型：微信收款码</view>
      <view class="credit-text">收款户名：{{creditInfo.accountNumber}}</view>
      <view class="credit-text">收款账号：{{creditInfo.accountNumber}}</view>
      <view class="credit-text">收款码：<image src="{{creditInfo.imageFileID}}" mode="aspectFill" class="credit-img"/>
      </view>
    </view>
    <view wx:if="{{creditInfo.accountType === 'alipay'}}" class="credit-card">
      <view class="credit-text">账号类型：支付宝</view>
      <view class="credit-text">收款户名：{{creditInfo.accountNumber}}</view>
      <view class="credit-text">收款账号：{{creditInfo.accountNumber}}</view>
    </view>
  </view>
</view>
<view class="box">
  <view class="container">
    <view class="form-item">
      <text class="label">物流公司</text>
      <picker mode="selector" range="{{logisticsCompanies}}" range-key="name" bindchange="bindLogisticsCompanyChange">
        <view class="picker {{selectedCompany ? '' : 'placeholder'}}">
          {{selectedCompany || '请选择物流公司'}}
        </view>
      </picker>
    </view>

    <view class="form-item">
      <text class="label">物流单号</text>
      <input class="input" placeholder="请输入物流单号" placeholder-class="placeholder" bindinput="bindWaybillInput" value="{{waybillNumber}}" />
    </view>

    <view class="form-item">
      <text class="label">货物数量</text>
      <input class="input" type="number" placeholder="输入寄出的机器数量,最少1台" placeholder-class="placeholder" bindinput="bindQuantityInput" value="{{quantity}}" />
    </view>

    <view class="form-item">
      <text class="label">备注信息</text>
      <input class="input" placeholder="首次下单请备注对接群的名称" placeholder-class="placeholder" bindinput="bindRemarkInput" value="{{remark}}" />
    </view>

    <view class="form-item">
      <text class="label">备注图片</text>
      <view class="uploader">
        <view class="upload-btn" bindtap="chooseImage" wx:if="{{!tempFilePaths}}">
          <icon type="add" size="20" color="#999"></icon>
          <text class="upload-text">上传图片</text>
        </view>
        <view class="preview" wx:if="{{tempFilePaths}}">
          <view style="position: relative;">
            <image src="{{imageFileID}}" mode="aspectFill" bindtap="previewImage" data-src="{{imageFileID}}" />
            <image src="../../images/remove-icon.png" mode="aspectFill" class="icon" bindtap="removeImage" />
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
<view style="width: 100%;height: 150rpx;"></view>
<view class="btn-bottom" bind:tap="submitForm">提交</view>