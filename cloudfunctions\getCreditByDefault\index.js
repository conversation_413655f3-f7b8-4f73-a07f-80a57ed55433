// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { OPENID } = wxContext // 获取当前用户的openid

  try {
    // 查询当前用户的默认收款账户
    const result = await db.collection('credit')
      .where({
        _openid: OPENID,
        isDefault: true
      })
      .get()

    // 检查查询结果
    if (result.data.length === 0) {
      return {
        code: 200,
        message: '未设置默认收款账户',
        data: null
      }
    }

    // 返回找到的默认账户（理论上应该只有一个）
    return {
      code: 200,
      message: '查询成功',
      data: result.data[0] // 返回第一条记录
    }
  } catch (err) {
    console.error('查询失败:', err)
    return {
      code: 500,
      message: '服务器错误',
      error: err
    }
  }
}