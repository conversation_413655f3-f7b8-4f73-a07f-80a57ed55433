// 云函数入口文件
const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  const db = cloud.database()
  
  try {
    // 查询notice表，按createTime升序排列
    const queryResult = await db.collection('notice')
      .orderBy('createTime', 'asc')
      .get()
    
    return {
      success: true,
      data: queryResult.data
    }
  } catch (err) {
    console.error('获取公告失败:', err)
    return {
      success: false,
      message: err.message
    }
  }
}