/**index.wxss**/
page {
  background-color: #f6f7f9;
}

.index_top_image {
  width: 100%;
}

.box {
  width: 92%;
  background-color: #fff;
  position: relative;
  margin: 20rpx auto 0;
  border-radius: 20rpx;
}

/* pages/store/store.wxss */
.store-container {
  padding: 20rpx;
  margin: 20rpx;
}

.store-header {
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
  padding-bottom: 15rpx;
  font-weight: 900;
}

.store-title {
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.store-location {
  font-size: 30rpx;
  color: #333;
}

.store-contact {
  margin-bottom: 10rpx;
  display: flex;
}

.contact-name {
  font-size: 28rpx;
  color: #444;
  margin-bottom: 10rpx;
  margin-right: 10rpx;
}

.contact-phone {
  font-size: 28rpx;
  color: #444;
  display: block;
}

.store-address {
  margin: 20rpx auto 0;
  font-size: 28rpx;
  color: #444;
  display: flex;
}

.store-distance {
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #999;
}

.t1 {
  font-size: 34rpx;
  font-weight: 600;
  padding: 22rpx 0;
  text-align: center;
}

.fgx {
  height: 1rpx;
  background-color: #cb9e5d;
}

.address-card {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.address-info {
  flex: 1;
}

.address-right-line {
  height: 120rpx;
  width: 1rpx;
  background-color: #cb9e5d;
  margin: 0 42rpx;
}

.name-phone {
  margin-bottom: 5rpx;
}

.name {
  font-size: 34rpx;
  color: #373737;
  margin-right: 40rpx;
}

.phone {
  font-size: 25rpx;
  color: #666;
}

.address {
  line-height: 45rpx;
}

.room {
  font-size: 25rpx;
  color: #666;
}

.copy-adddress {
  justify-content: center;
}

.copy-image-container {
  text-align: center;
}

.copy-img{
  width: 45rpx;
  height: 45rpx;
}

.copy-text {
  font-size: 23rpx;
  color: #838383;
}

.btn-card{
  width: 95%;
  height: 120rpx;
  background-color: #f6f8fe;
  margin: 0 auto;
  border-radius: 10rpx;
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

.gradient-btn {
  /* 渐变背景 */
  background: linear-gradient(90deg, #555, #555); /* 从左到右：橙 → 深橙 */
  
  /* 文字样式 */
  color: white;
  font-size: 40rpx;
  font-weight: bold;
  text-align: center;
  
  /* 按钮形状 */
  width: 95%;
  height: 90rpx;
  line-height: 80rpx; /* 垂直居中 */
  border-radius: 10rpx; /* 圆角 */
}

.box2 {
  width: 92%;
  background-color: #fff;
  position: relative;
  margin: 20rpx auto;
  border-radius: 20rpx;
}

.customer-service {
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  padding: 20rpx 0;
}

.customer-service .text {
  margin: 0 20rpx; /* 控制文字与横线的间距 */
  font-size: 26rpx;
  color: #b3b3b3;
  white-space: nowrap; /* 防止文字换行 */
}

.line {
  height: 1px; /* 横线高度 */
  width: 180rpx; /* 横线长度 */
}

/* 左侧横线：从透明渐变到颜色 */
.left-line {
  background: linear-gradient(to left, #ddd, transparent);
}

/* 右侧横线：从颜色渐变到透明 */
.right-line {
  background: linear-gradient(to right, #ddd, transparent);
}

.kf-box {
  width: 100%;
  display: flex;
  justify-content: center;
  /* margin: 10rpx 10rpx; */
}

.kf-card {
  margin: 0 15rpx;
}

/* 通知栏整体样式 */
.notice-bar {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background-color: #fef9f7; /* 浅黄色背景 */
  border-radius: 20rpx;
  margin: 20rpx 25rpx 0rpx;
}

/* 喇叭图标样式 */
.horn-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

/* 文字滚动容器 */
.scroll-container {
  flex: 1;
  height: 40rpx; /* 单行文字高度 */
  overflow: hidden;
}

/* 文字轮播样式 */
.text-swiper {
  height: 100%;
}

.notice-text {
  font-size: 14px;
  color: #838282;
  line-height: 40rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 选项容器 */
.tab-container {
  display: flex;
  width: 100%;
  margin: 25rpx auto;
  overflow: hidden;
  background-color: #555; /* 未选中时的黄色背景 */
  border-top-right-radius: 20rpx;
  border-top-left-radius: 20rpx;
}

/* 单个选项 */
.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 32rpx;
  color: white; /* 未选中时白色文字 */
}

/* 选中状态 */
.tab-item.active {
  background-color: white;
  color: black;
  font-weight: 900;
  border-bottom: 1rpx solid #cb9e5d;
}

/* 品牌展示容器 */
.brand-container {
  display: flex;
  flex-wrap: wrap; /* 允许换行 */
  justify-content: flex-start;
  padding: 0rpx 20rpx;
  background-color: #fff;
}

/* 单个品牌项目 */
.brand-item {
  width: 25%; /* 每行4个 */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  box-sizing: border-box;
}

/* 品牌图片 */
.brand-image {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 5rpx;
}

/* 品牌名称 */
.brand-name {
  font-size: 23rpx;
  color: #9e9e9e;
  text-align: center;
}

/* 折叠面板容器 */
.accordion-container {
  width: 84%;
  margin: 20rpx auto;
}

/* 单个折叠项 */
.accordion-item {
  margin-bottom: 5rpx;
  overflow: hidden;
}

/* 面板标题 */
.accordion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0rpx;
  font-size: 28rpx;
  border-bottom: 1rpx solid #e4e4e4;
}

.accordion-header text {
  color: #8a8a8a;
}

/* 箭头图标 */
.arrow-icon {
  width: 30rpx;
  height: 30rpx;
  transition: transform 0.3s;
}

/* 面板内容 */
.accordion-content {
  overflow: hidden;
  transition: height 0.3s ease;
  background-color: #fff;
}

/* 内容区域 */
.content-inner {
  padding: 20rpx 30rpx;
  font-size: 24rpx;
  color: rgb(148, 148, 148);
}

/* 样式 */
.preview-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: black;
  z-index: 999;
}
.image-info {
  bottom: 20px;
  left: 0;
  width: 100%;
  text-align: center;
  color: white;
  background: rgba(0, 0, 0, 0.5);
  padding: 10px;
  z-index: 1000;
}

.close-btn {
  position: fixed;
  top: 10px;
  right: 10px;
  color: white;
  font-size: 20px;
  z-index: 10000;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 0 12rpx;
}

/* 容器样式 */
.container {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  margin: 0 20rpx;
  padding-bottom: 30rpx;
}

/* 卡片样式 */
.card {
  width: 200rpx;
  height: 260rpx;
  border-radius: 16rpx;
  background: linear-gradient(to bottom, #e6f0fc, #7ec3fe);
  box-shadow: 0 10rpx 20rpx rgba(126, 195, 254, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 20rpx;
  box-sizing: border-box;
}

/* 图标容器 */
.icon-container {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 5rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
}

/* 图标样式 */
.icon {
  width: 100rpx;
  height: 100rpx;
}

/* 标题样式 */
.title {
  font-size: 30rpx;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 10rpx;
}

/* 副标题样式 */
.subtitle {
  font-size: 20rpx;
  color: #333;
  margin-bottom: 5rpx;
}

/* 描述样式 */
.description {
  font-size: 20rpx;
  color: #666;
}