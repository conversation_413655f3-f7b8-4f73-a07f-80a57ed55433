// pages/order/order.js
Page({
  data: {
    searchText: '', // 搜索文本
    activeStatus: 0, // 当前选中状态
    statusList: [{
        label: '全部',
        value: 0
      },
      {
        label: '验机中',
        value: 1
      },
      {
        label: '待确认',
        value: 2
      },
      {
        label: '待结算',
        value: 3
      },
      {
        label: '已完成',
        value: 4
      },
      {
        label: '退回',
        value: 5
      }
    ],
    statusOptions: ['运输中', '验机中', '待确认', '待结算', '已完成', '退回'],
    orderList: [], // 订单列表数据
    isLoading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    total: 0,
    searchFlag: false, // 判断是否有查询过
    currentId: ''
  },

  // 退回
  handleReback(e){
    wx.showModal({
      title: '提示',
      content: `确定要退回吗？`,
      confirmText: '确认',
      confirmColor: '#FA5151',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            currentId: e.currentTarget.dataset.id
          },()=>{
            this.updateOrderSatus(5,undefined,e.currentTarget.dataset.index);
          })
        }
      }
    });
  },

  // 更改订单状态
  updateOrderSatus(status,amount,index) {
    wx.showLoading({
      title: '提交中...',
    })
    // 在小程序中调用
    wx.cloud.callFunction({
      name: 'updateOrders',
      data: {
        id: this.data.currentId, // 必须 - 订单ID
        status: status, // 可选 - 要更新的状态
        amount: amount // 可选 - 要更新的金额
      },
      success: res => {
        wx.hideLoading()
        if(res.result.code==200){
          if(this.data.activeStatus==0){
            this.setData({
              [`orderList[${index}].status`]: status,
              [`orderList[${index}].amount`]: amount || null
            })
          }else {
            this.setData({
              orderList: this.data.orderList.filter((_, i) => i !== index)
            })
          }
        }
      },
      fail: err => {
        wx.hideLoading()
        console.error('更新失败:', err)
      }
    })
  },

  // 同意报价
  handleConfirmQuote(e){
    this.setData({
      currentId: e.currentTarget.dataset.id
    },()=>{
      this.updateOrderSatus(3,undefined,e.currentTarget.dataset.index);
    })
  },

  handleMore() {
    this.loadOrderList();
  },

  onLoad(options) {
    if(options.status){
      this.setData({
        activeStatus: Number(options.status)
      })
    }
    // 加载初始数据
    this.loadOrderList();
  },

  // 处理搜索输入
  handleSearchInput(e) {
    this.setData({
      searchText: e.detail.value
    });
  },

  // 执行搜索
  handleSearch() {
    console.log(this.data.searchText);
    this.setData({
      searchFlag: true,
      page: 1,
      total: 0,
      hasMore: true,
      orderList: [],
    }, () => {
      this.loadOrderList();
    });
  },

  // 切换订单状态
  changeStatus(e) {
    const status = e.currentTarget.dataset.status;
    if (status != this.data.activeStatus) {
      this.setData({
        activeStatus: status,
        page: 1,
        total: 0,
        hasMore: true,
        orderList: [],
        searchFlag: false
      }, () => {
        this.loadOrderList();
      });
    }
  },

  // 加载订单数据
  async loadOrderList() {
    if (this.data.isLoading || !this.data.hasMore) return;

    this.setData({
      isLoading: true
    });

    try {
      const newOrders = await this.getMockData();

      this.setData({
        orderList: [...this.data.orderList, ...newOrders],
        isLoading: false,
        hasMore: this.data.orderList.length < this.data.total,
        page: this.data.page + 1
      });

    } catch (error) {
      this.setData({
        isLoading: false
      });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 获取数据
  getMockData() {
    return new Promise((resolve) => {
      wx.cloud.callFunction({
        name: 'getOrderList',
        data: {
          status: this.data.activeStatus,
          page: this.data.page,
          searchText: this.data.searchFlag ? this.data.searchText : ''
        },
        success: (res) => {
          this.setData({
            total: res.result.pagination.total
          });
          const list = res.result.data.map(item => ({
            ...item,
            createdTime: this.formatDateTime(item.createdTime)
          }));
          resolve(list);
        },
        fail: (err) => {
          console.error('查询失败:', err);
          resolve([]);
        }
      });
    });
  },

  // 时间转换
  formatDateTime(isoString) {
    const date = new Date(isoString);

    // 获取年月日
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    // 获取时分
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  // 预览图片
  previewImage(e) {
    const src = e.currentTarget.dataset.src;
    wx.previewImage({
      urls: [src],
      current: src
    });
  },

  // 删除订单操作
  handleDelete(e) {
    wx.showModal({
      title: '提示',
      content: `删除后订单无法恢复，请慎重考虑`,
      confirmText: '确认',
      confirmColor: '#FA5151',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
          })
          this.setData({
            currentId: e.currentTarget.dataset.id
          },()=>{
            wx.cloud.callFunction({
              name: 'softDeleteOrder',
              data: {
                _id: e.currentTarget.dataset.id
              }
            }).then(res => {
              wx.hideLoading()
              this.setData({
                [`orderList[${e.currentTarget.dataset.index}].isDelete`]: true
              })
            }).catch(err => {
              console.error(err)
            })
          })
        }
      }
    });
  },
});