// pages/goodsSearch/goodsSearch.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    goodsList: [],
    searchValue: '',
    wxmID: 'cloud://' + app.globalData.imgEnv + '/images/ewm.jpg'
  },

  // 点击商品
  handleClick(){
    wx.showModal({
      title: '提示',
      content: '请联系客服获取更多详情',
      success: (res) => {
        if (res.confirm) {
          wx.previewImage({
            current: this.data.wxmID,
            urls: [this.data.wxmID]
          })
        }
      }
    })
  },

  onSearch: function(e) {
    wx.cloud.callFunction({
      name: 'getGoods',
      data: {
        searchText: this.data.searchValue
      },
      success: res => {
        this.setData({
          goodsList: res.result.data
        })
      }
    })
  },
  
  onInput: function(e) {
    this.setData({
      searchValue: e.detail.value
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})