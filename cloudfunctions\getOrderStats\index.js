// 云函数入口文件
const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const db = cloud.database()
  const _ = db.command // 获取数据库命令
  const $ = db.command.aggregate // 获取聚合操作符
  
  try {
    // 查询status不为5的订单数量
    const countResult = await db.collection('orders') // 请替换为你的集合名
      .where({
        _openid: wxContext.OPENID,
        status: _.neq(5) // 使用_.neq代替db.command.neq
      })
      .count()
    
    // 查询status为4的订单amount总和
    const aggregateResult = await db.collection('orders')
      .aggregate()
      .match({
        _openid: wxContext.OPENID,
        status: 4
      })
      .group({
        _id: null,
        totalAmount: $.sum('$amount') // 现在$已正确定义
      })
      .end()
    
    // 提取总金额，如果没有符合条件的订单则返回0
    const totalAmount = aggregateResult.list.length > 0 
      ? aggregateResult.list[0].totalAmount 
      : 0
    
    return {
      success: true,
      data: {
        orderCount: countResult.total, // status不为5的订单数量
        totalAmount: totalAmount       // status为4的订单amount总和
      }
    }
  } catch (err) {
    console.error(err)
    return {
      success: false,
      message: err.message
    }
  }
}