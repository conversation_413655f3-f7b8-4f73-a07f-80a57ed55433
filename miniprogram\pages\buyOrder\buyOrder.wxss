/* pages/order/order.wxss */
page {
  background-color: #f6f7f9;
}

.container {
  padding: 20rpx;
}

/* 状态选择器 */
.status-container {
  background: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.status-scroll {
  white-space: nowrap;
  width: 100%;
  height: 80rpx;
}

/* 隐藏滚动条 */
.status-scroll ::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.status-item {
  display: inline-block;
  padding: 0 30rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.status-item.active {
  color: #5677fc;
  font-weight: bold;
}

.status-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #5677fc;
}

/* pages/order/list.wxss */
.page-container {
  width: 100%;
}

.loading-text {
  text-align: center;
  font-size: 26rpx;
  color: #999;
  padding: 20rpx 0;
}