// 云函数入口文件
const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const db = cloud.database()
  
  // 解构参数
  const {
    type,          // 类型：1或2
    consignee,     // 收货人
    phone,         // 电话
    province,      // 省
    city,          // 市
    district,      // 区
    detail,        // 详细地址
    is_default = true // 是否默认地址
  } = event
  
  if (!consignee || !phone || !province || !city || !district || !detail) {
    return {
      code: 400,
      message: '缺少必要参数'
    }
  }
  
  // 手机号校验简单正则
  const phoneReg = /^1[3-9]\d{9}$/
  if (!phoneReg.test(phone)) {
    return {
      code: 400,
      message: '手机号格式不正确'
    }
  }
  
  try {
    // 检查是否已存在相同类型的地址
    const checkResult = await db.collection('address_book')
      .where({
        openid: wxContext.OPENID,
        type: type
      })
      .count()
    
    if (checkResult.total > 0) {
      return {
        code: 400,
        message: '只能添加一个'
      }
    }
    
    // 添加新地址
    const result = await db.collection('address_book').add({
      data: {
        openid: wxContext.OPENID,  // 用户openid
        type,                     // 类型
        consignee,                // 收货人
        phone,                    // 电话
        province,                 // 省
        city,                    // 市
        district,                 // 区
        detail,                   // 详细地址
        is_default,               // 是否默认地址
        createTime: db.serverDate() // 创建时间
      }
    })
    
    return {
      code: 200,
      message: '添加成功',
      data: result
    }
  } catch (err) {
    console.error(err)
    return {
      code: 500,
      message: '服务器错误',
      error: err
    }
  }
}