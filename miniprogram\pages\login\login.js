// pages/login/login.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    phoneNumber: '',
    isPhoneValid: false,
    agreeProtocol: false
  },

  // 新增：处理协议勾选状态变化
  handleAgreeChange(e) {
    this.setData({
      agreeProtocol: e.detail.value.includes('agree')
    })
  },

  // 手机号输入处理
  handlePhoneInput(e) {
    const phone = e.detail.value
    this.setData({
      phoneNumber: phone,
      isPhoneValid: this.validatePhone(phone)
    })
  },

  // 手机号校验
  validatePhone(phone) {
    const reg = /^1[3-9]\d{9}$/
    return reg.test(phone)
  },

  // 登录处理
  handleLogin() {
    if (!this.data.isPhoneValid) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return
    }

    // 新增：检查是否同意协议
    if (!this.data.agreeProtocol) {
      wx.showToast({
        title: '请阅读并同意用户协议和隐私政策',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '登录中...',
    })
    // 登录请求
    this.onLogin();
  },

  // 跳转协议页面
  goToProtocol(){
    wx.navigateTo({
      url: '../protocol/protocol',
    })
  },

  /**
   * 微信登录
   */
  onLogin() {
    const phone = this.data.phoneNumber
    wx.getUserProfile({
      desc: '用于完善用户信息',
      success(res) {
        res.userInfo.phone = phone
        wx.cloud.callFunction({
          name: 'wechatLogin',
          data: {
            userInfo: res.userInfo
          },
          success(res) {
            wx.hideLoading()
            wx.setStorageSync('phone', phone);
            wx.navigateBack();
            wx.showToast({
              title: '登录成功',
              icon: 'success'
            })
          },
          fail: (err) => {
            wx.hideLoading()
            console.log("登录失败", err);
            wx.showToast({
              title: '登录失败',
              icon: 'none'
            })
          }
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})