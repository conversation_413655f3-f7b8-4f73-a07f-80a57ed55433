// 云函数入口文件
const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()

// 生成订单号 (格式: ORD + 年月日 + 6位随机数)
function generateOrderNo() {
  const now = new Date()
  const datePart = [
    now.getFullYear(),
    String(now.getMonth() + 1).padStart(2, '0'),
    String(now.getDate()).padStart(2, '0')
  ].join('')
  const randomPart = Math.floor(100000 + Math.random() * 900000)
  return `ORD${datePart}${randomPart}`
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { OPENID } = wxContext
  
  // 从event中获取订单数据
  const {
    creditId,
    imageFileID,
    quantity,
    remark,
    selectedCompany,
    waybillNumber
  } = event

  // 参数校验
  if (!creditId || !quantity || !selectedCompany || !waybillNumber) {
    return {
      code: 400,
      message: '缺少必要参数(creditId/quantity/selectedCompany/waybillNumber)'
    }
  }

  try {
    // 1. 生成订单数据
    const orderData = {
      creditId,          // 关联的收款账户ID
      imageFileID,       // 图片文件ID
      quantity: Number(quantity), // 转换为数字
      remark: remark || '', // 备注(可选)
      selectedCompany,   // 快递公司
      waybillNumber,     // 运单号
      orderNo: generateOrderNo(), // 生成的订单号
      status: 0, // 订单状态(默认待处理)
      _openid: OPENID,   // 用户openid
      createdTime: db.serverDate(), // 服务器时间
      updatedTime: db.serverDate(),  // 更新时间
      amount: 0,
      isDelete: false, // 用户是否删除
    }

    // 2. 验证关联的creditId是否存在
    const creditRecord = await db.collection('credit')
      .doc(creditId)
      .get()
    
    if (!creditRecord.data) {
      return {
        code: 404,
        message: '关联的收款账户不存在'
      }
    }

    // 3. 插入订单数据
    const result = await db.collection('orders').add({
      data: orderData
    })

    return {
      code: 200,
      message: '订单创建成功',
      data: {
        _id: result._id,         // 数据库自动生成的_id
        orderNo: orderData.orderNo, // 我们生成的订单号
        createdTime: orderData.createdTime
      }
    }
  } catch (err) {
    console.error('订单创建失败:', err)
    return {
      code: 500,
      message: '服务器错误',
      error: err
    }
  }
}