// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database();
// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const {userInfo} = event;
  const {OPENID} = wxContext;
  const now = new Date();
  try{
    const res = await db.collection('users').where({
      openid: OPENID
    }).get();
    if(res.data.length>0){
      await db.collection('users').doc(res.data[0]._id).update({
        data: {
          lastLoginTime: now
        }
      })
    } else{
      await db.collection('users').add({
        data: {
          openid: OPENID,
          userName: userInfo.nickName,
          lastLoginTime: now,
          createTime: now,
          phone: userInfo.phone
        }
      })
    }
    return {
      success: true
    }
  } catch(e) {
    console.log(e);
    return {
      success: false
    }
  }
}