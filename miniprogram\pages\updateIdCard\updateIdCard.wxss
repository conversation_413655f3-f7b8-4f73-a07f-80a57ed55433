.container {
  padding: 30rpx;
}

.upload-section {
  margin-bottom: 40rpx;
}

.upload-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.upload-box {
  width: 100%;
  height: 250rpx; /* 减小高度 */
  border: 2rpx dashed #ccc;
  border-radius: 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9;
  position: relative;
  overflow: hidden;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}

.upload-icon {
  width: 60rpx; /* 减小图标大小 */
  height: 60rpx;
  margin-bottom: 15rpx;
}

.upload-image {
  width: 100%;
  max-height: 250rpx; /* 限制最大高度 */
  display: block;
}

.delete-icon {
  position: absolute;
  right: 10rpx;
  top: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  z-index: 10;
}

.submit-btn {
  margin-top: 60rpx;
  background-color: #07c160;
  color: white;
  border-radius: 50rpx;
  font-size: 32rpx;
}

.submit-btn[disabled] {
  background-color: #cccccc;
  color: #999999;
}