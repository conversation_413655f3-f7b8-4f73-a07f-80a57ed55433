// 云函数入口文件
const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { OPENID } = wxContext
  const { 
    page = 1, 
    pageSize = 10,
    status = 0, // 默认0查询全部
    searchText = ''
  } = event

  // 状态码校验
  if (![0, 1, 2, 3, 4, 5].includes(Number(status))) {
    return {
      code: 400,
      message: 'status参数错误，请传入0-5的整数'
    }
  }

  try {
    // 1. 构建基础查询条件
    let query = _.and([{ _openid: OPENID }]);
    query = _.and([{ isDelete: false }]);

    // 2. 添加状态条件
    if (Number(status) !== 0) {
      query = _.and([query, { status: status }]);
    }

    // 3. 添加搜索条件
    if (searchText.trim()) {
      const searchRegex = db.RegExp({
        regexp: searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
        options: 'i'
      });
      query = _.and([
        query,
        _.or([
          { orderNo: searchRegex },
          { waybillNumber: searchRegex }
        ])
      ]);
    }

    // 2. 查询订单列表（按创建时间倒序）
    const orderRes = await db.collection('orders')
      .where(query.valueOf())
      .orderBy('createdTime', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get()

    // 3. 批量查询关联的收款账户信息（原有逻辑保持不变）
    if (orderRes.data.length === 0) {
      return {
        code: 200,
        message: '暂无订单数据',
        data: [],
        pagination: {
          total: 0,
          page,
          pageSize,
          currentStatus: status
        }
      }
    }

    const creditIds = orderRes.data.map(order => order.creditId)
    const creditRes = await db.collection('credit')
      .where({ _id: _.in(creditIds) })
      .get()

    // 4. 合并数据并返回
    const creditMap = {}
    creditRes.data.forEach(credit => { creditMap[credit._id] = credit })

    const countRes = await db.collection('orders')
      .where(query.valueOf())
      .count()

    return {
      code: 200,
      message: '查询成功',
      data: orderRes.data.map(order => ({
        ...order,
        creditInfo: creditMap[order.creditId] || null
      })),
      pagination: {
        total: countRes.total,
        page,
        pageSize,
        currentStatus: status, // 返回当前筛选状态
        searchText
      }
    }
  } catch (err) {
    console.error('查询失败:', err)
    return {
      code: 500,
      message: '服务器错误',
      error: err
    }
  }
}