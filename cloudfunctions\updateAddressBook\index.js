// 云函数入口文件
const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const db = cloud.database()
  const _ = db.command

  // 解构参数
  const {
    id,            // 必填，要修改的地址ID
    type,          // 可选，类型：1或2
    consignee,     // 可选，收货人
    phone,         // 可选，电话
    province,      // 可选，省
    city,          // 可选，市
    district,      // 可选，区
    detail,        // 可选，详细地址
    is_default     // 可选，是否默认地址
  } = event

  // 参数校验
  if (!id) {
    return {
      code: 400,
      message: '缺少地址ID参数'
    }
  }

  // 手机号校验（如果提供了phone参数）
  if (phone) {
    const phoneReg = /^1[3-9]\d{9}$/
    if (!phoneReg.test(phone)) {
      return {
        code: 400,
        message: '手机号格式不正确'
      }
    }
  }

  // 类型校验（如果提供了type参数）
  if (type && (type !== '1' && type !== '2')) {
    return {
      code: 400,
      message: '类型必须为1或2'
    }
  }

  try {
    // 1. 验证该地址属于当前用户
    const checkResult = await db.collection('address_book')
      .where({
        _id: id,
        openid: wxContext.OPENID
      })
      .count()

    if (checkResult.total === 0) {
      return {
        code: 404,
        message: '未找到该地址或无权修改'
      }
    }

    // 2. 构建更新数据对象
    const updateData = {}
    const fields = ['type', 'consignee', 'phone', 'province', 'city', 'district', 'detail', 'is_default']
    
    fields.forEach(field => {
      if (event[field] !== undefined) {
        updateData[field] = event[field]
      }
    })

    // 4. 执行更新
    const result = await db.collection('address_book')
      .doc(id)
      .update({
        data: updateData
      })

    return {
      code: 200,
      message: '更新成功',
      data: result
    }
  } catch (err) {
    console.error(err)
    
    // 处理特定错误
    if (err.errCode === 'DATABASE_PERMISSION_DENIED') {
      return {
        code: 403,
        message: '无权修改该地址'
      }
    }

    return {
      code: 500,
      message: '更新地址失败',
      error: err.message
    }
  }
}