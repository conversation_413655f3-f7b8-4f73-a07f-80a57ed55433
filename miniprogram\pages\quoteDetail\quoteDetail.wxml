<!--pages/quoteDetail/quoteDetail.wxml-->
<view class="container">
  <!-- 水印输入区域 -->
  <view class="watermark-section">
    <text class="label">报价单水印：</text>
    <input class="watermark-input" placeholder="{{watermarkText}}" bindinput="onWatermarkInput" />
  </view>

  <!-- 颜色选择区域 -->
  <view class="color-section">
    <view class="color-row">
      <text class="label">报价单颜色：</text>
      <view class="color-grid">
        <view wx:for="{{colorOptions}}" wx:key="id"
              class="color-item {{selectedColorId === item.id ? 'selected' : ''}} {{item.name === '简约白' ? 'white-text' : ''}}"
              bindtap="onColorSelect"
              data-color-id="{{item.id}}"
              style="background: {{item.gradient}};">
          {{item.name}}
        </view>
      </view>
    </view>
  </view>

  <!-- 手机型号列表 -->
  <view class="phone-list">
    <view wx:for="{{phoneModels}}" wx:key="id" class="phone-item {{item.selected ? 'selected' : ''}}">
      <!-- 选择框 -->
      <view class="checkbox-container" bindtap="onPhoneSelect" data-index="{{index}}">
        <view class="checkbox {{item.selected ? 'checked' : ''}}">
          <text wx:if="{{item.selected}}" class="checkmark">✓</text>
        </view>
      </view>

      <!-- 手机信息 -->
      <view class="phone-info">
        <view class="phone-row">
          <text class="phone-label">当前报价单：</text>
          <text class="phone-model">{{item.name}}</text>
        </view>

        <view class="phone-row">
          <text class="phone-label">整机修改：</text>
          <view class="condition-selector" bindtap="onConditionSelect" data-index="{{index}}">
            <text class="condition-text">{{item.condition}}</text>
            <text class="dropdown-arrow">▼</text>

            <!-- 下拉选项 -->
            <view wx:if="{{item.showDropdown}}" class="dropdown-options">
              <view wx:for="{{conditionOptions}}" wx:key="*this" wx:for-item="option" wx:for-index="optionIndex"
                    class="dropdown-option"
                    bindtap="onConditionOptionSelect"
                    data-phone-index="{{index}}"
                    data-option-index="{{optionIndex}}">
                {{option}}
              </view>
            </view>
          </view>
        </view>

        <view class="price-row">
          <input class="price-input" type="number" value="{{item.price}}" />
          <text class="currency">元</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-buttons">
    <view class="btn-secondary" bindtap="onPersonalizeAdjust">个性化调整</view>
    <view class="btn-primary" bindtap="onGenerateQuote">生成报价单</view>
  </view>
</view>
