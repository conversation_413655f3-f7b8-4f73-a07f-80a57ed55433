<!--pages/goods/goods.wxml-->
<view style="height: 180rpx;width: 100%;text-align: center;line-height: 250rpx;background-color: #292927;color: white;">深圳麒麟通讯</view>
<view class="image-container" style="position: relative;">
  <image src="{{fileID}}" mode="aspectFill" style="width: 100%;height: 360rpx;" />
  <view style="position: absolute;top: 9%;left: 5%;width: 90%;height: 80rpx;background-color: #fff;border-radius: 8rpx;display: flex;align-items: center;z-index: 2;" bind:tap="handleSearch">
    <text style="padding-left: 35rpx;font-size: 30rpx;color: #8e9ab0;">好货直卖数码市场，品质保证，售后保障</text>
  </view>
</view>
<view class="box">
  <view style="display: flex;flex-direction: column;align-items: center;padding: 10rpx 25rpx 10rpx 35rpx;" bind:tap="handleSearchBySeries" data-series="0">
    <image src="{{iphoneImg}}" mode="aspectFill" style="width: 60rpx;height: 80rpx;" />
    <view style="font-size: 20rpx;color: #696969;margin-top: 10rpx;">拆机原屏</view>
  </view>
  <view style="display: flex;flex-direction: column;align-items: center;padding: 10rpx 25rpx;" bind:tap="handleSearchBySeries" data-series="1">
    <image src="{{andriodImg}}" mode="aspectFill" style="width: 60rpx;height: 80rpx;" />
    <view style="font-size: 20rpx;color: #696969;margin-top: 10rpx;">无标后盖</view>
  </view>
  <view style="display: flex;flex-direction: column;align-items: center;padding: 10rpx 25rpx;" bind:tap="handleSearchBySeries" data-series="2">
    <image src="{{ipadImg}}" mode="aspectFill" style="width: 100rpx;height: 80rpx;" />
    <view style="font-size: 20rpx;color: #696969;margin-top: 10rpx;">手机中框</view>
  </view>
  <view style="display: flex;flex-direction: column;align-items: center;padding: 10rpx 25rpx;" bind:tap="handleSearchBySeries" data-series="3">
    <image src="{{computerImg}}" mode="aspectFill" style="width: 100rpx;height: 60rpx;padding: 12rpx 0;" />
    <view style="font-size: 20rpx;color: #696969;margin-top: 10rpx;">主板兑换</view>
  </view>
</view>
<view class="container">
  <view class="goods-list">
    <block wx:for="{{goodsList}}" wx:key="_id">
      <view class="goods-item" bind:tap="handleClick">
        <image class="goods-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="goods-info">
          <text class="goods-name">{{item.name}}</text>
          <text class="goods-price">￥{{item.price}}</text>
        </view>
      </view>
    </block>
    <view class="no-more">
      <text>没有更多了</text>
    </view>
  </view>
</view>
<custom-tab-bar></custom-tab-bar>