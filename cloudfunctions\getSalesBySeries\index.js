// 云函数入口文件
const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    const db = cloud.database()
    
    // 并行查询series为0和1的数据
    const [series0Result, series1Result] = await Promise.all([
      db.collection('sales_qutote')
        .where({ series: 0 })
        .orderBy('sort', 'desc')
        .get(),
      db.collection('sales_qutote')
        .where({ series: 1 })
        .orderBy('sort', 'desc')
        .get()
    ])
    
    return {
      code: 0,
      data: {
        series0: series0Result.data,
        series1: series1Result.data
      },
      message: '查询成功'
    }
  } catch (err) {
    console.error(err)
    return {
      code: -1,
      data: {
        series0: [],
        series1: []
      },
      message: '查询失败: ' + err.message
    }
  }
}