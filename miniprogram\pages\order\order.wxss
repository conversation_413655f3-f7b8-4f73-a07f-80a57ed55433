/* pages/order/order.wxss */
page {
  background-color: #e9eaec;
}

.box {
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  width: 92%;
  margin: 25rpx auto;
}

/* 容器样式 */
.info-container {
  padding: 20rpx 20rpx 20rpx 30rpx;
  width: 100%;
}

/* 第一行样式 - 姓名和电话 */
.info-line {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-right: 30rpx;
}

.phone {
  font-size: 28rpx;
  color: #666666;
}

/* 地址行样式 */
.address-line {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  padding-bottom: 20rpx;
}

.chose-pay-way {
  color: #5677fc;
  width: 100%;
  text-align: center;
  padding: 30rpx 0;
}

.btn-bottom {
  width: 92%;
  height: 90rpx;
  background-color: #007aff;
  color: white;
  font-size: 32rpx;
  justify-content: center;
  display: flex;
  align-items: center;
  border-radius: 12rpx;
  position: fixed;
  left: 4%;
  right: 0;
  bottom: 12rpx;
  z-index: 10;
}

.container {
  padding: 30rpx;
}

.form-item {
  padding: 30rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  font-size: 28rpx;
  border-bottom: 1rpx solid #eee;
  padding: 10rpx 0;
}

.picker {
  width: 100%;
  height: 80rpx;
  font-size: 28rpx;
  border-bottom: 1rpx solid #eee;
  padding: 10rpx 0;
  display: flex;
  align-items: center;
}

.picker.placeholder {
  color: #999;
}

.uploader {
  margin-top: 20rpx;
}

.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 180rpx;
  height: 180rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.preview {
  margin-top: 20rpx;
  display: flex;
  flex-wrap: wrap;
}

.preview image {
  width: 180rpx;
  height: 180rpx;
  margin-right: 10rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  position: relative;
}

.preview .icon {
  position: absolute;
  right: -10rpx;
  top: -10rpx;
  background: #fff;
  border-radius: 50%;
  z-index: 10;
  width: 40rpx;
  height: 40rpx;
}

.credit-card {
  padding: 20rpx 0 20rpx 30rpx;
}

.credit-card .credit-text {
  color: #666;
  line-height: 55rpx;
  width: 90%;          /* 设定宽度 */
  white-space: nowrap;    /* 不换行 */
  overflow: hidden;       /* 超出隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
  font-size: 30rpx;
}

.credit-card .credit-img {
  width: 120rpx;
  height: 120rpx;
}