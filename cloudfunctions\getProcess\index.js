// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 初始化数据库
const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    // 查询process集合，按sort字段降序排序
    const result = await db.collection('process')
      .orderBy('sort', 'desc')  // 按sort字段降序排序
      .get()
    
    // 返回查询结果
    return {
      code: 0,
      data: result.data,
      message: '查询成功'
    }
  } catch (err) {
    // 错误处理
    console.error('查询失败：', err)
    return {
      code: 1,
      data: null,
      message: '查询失败：' + err.message
    }
  }
}