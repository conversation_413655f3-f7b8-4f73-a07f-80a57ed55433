/* pages/qutote/qutote.wxss */
page {
  background-color: #f6f7f9;
}

.box2 {
  width: 92%;
  background-color: #fff;
  position: relative;
  margin: 20rpx auto;
  border-radius: 20rpx;
}

.tab-container {
  display: flex;
  width: 100%;
  margin: 25rpx auto;
  overflow: hidden;
  background-color: #cb9e5d; /* 未选中时的黄色背景 */
  border-top-right-radius: 20rpx;
  border-top-left-radius: 20rpx;
}

/* 单个选项 */
.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 32rpx;
  color: white; /* 未选中时白色文字 */
}

/* 选中状态 */
.tab-item.active {
  background-color: white;
  color: black;
  font-weight: 900;
  border-bottom: 1rpx solid #cb9e5d;
}

/* 品牌展示容器 */
.brand-container {
  display: flex;
  flex-wrap: wrap; /* 允许换行 */
  justify-content: flex-start;
  padding: 0rpx 20rpx;
  background-color: #fff;
}

/* 单个品牌项目 */
.brand-item {
  width: 25%; /* 每行4个 */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  box-sizing: border-box;
}

/* 品牌图片 */
.brand-image {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 5rpx;
}

/* 品牌名称 */
.brand-name {
  font-size: 23rpx;
  color: #9e9e9e;
  text-align: center;
}

.customer-service {
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  padding: 20rpx 0;
}

.customer-service .text {
  margin: 0 20rpx; /* 控制文字与横线的间距 */
  font-size: 26rpx;
  color: #b3b3b3;
  white-space: nowrap; /* 防止文字换行 */
}

.line {
  height: 1px; /* 横线高度 */
  width: 180rpx; /* 横线长度 */
}

/* 左侧横线：从透明渐变到颜色 */
.left-line {
  background: linear-gradient(to left, #ddd, transparent);
}

/* 右侧横线：从颜色渐变到透明 */
.right-line {
  background: linear-gradient(to right, #ddd, transparent);
}


/* 样式 */
.preview-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: black;
  z-index: 999;
}
.image-info {
  bottom: 20px;
  left: 0;
  width: 100%;
  text-align: center;
  color: white;
  background: rgba(0, 0, 0, 0.5);
  padding: 10px;
  z-index: 1000;
}

.close-btn {
  position: fixed;
  top: 10px;
  right: 10px;
  color: white;
  font-size: 20px;
  z-index: 10000;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 0 10rpx;
}
