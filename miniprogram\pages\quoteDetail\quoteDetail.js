// pages/quoteDetail/quoteDetail.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    phoneModels: [
      {
        id: 1,
        name: '靓机/小花',
        condition: '按金额下调',
        price: 200,
        selected: true,
        showDropdown: false
      },
      {
        id: 2,
        name: '花机/内爆',
        condition: '按金额下调',
        price: 200,
        selected: true,
        showDropdown: false
      },
      {
        id: 3,
        name: '卡贴外版',
        condition: '按金额下调',
        price: 200,
        selected: true,
        showDropdown: false
      },
      {
        id: 4,
        name: '外版无锁',
        condition: '按金额下调',
        price: 200,
        selected: true,
        showDropdown: false
      }
    ],
    conditionOptions: ['按金额下调', '按比例下调（自动取整十数）'],
    colorOptions: [
      { id: 0, name: '高级黑', gradient: 'linear-gradient(135deg, #000000, #b3b3b3)' },
      { id: 1, name: '尊贵金', gradient: 'linear-gradient(135deg, #b8860b, #f2d78b)' },
      { id: 2, name: '橄榄绿', gradient: 'linear-gradient(135deg, #90ac71, #90ac71)' },
      { id: 3, name: '通用蓝', gradient: 'linear-gradient(135deg, #72a1d5, #82b8f8)' },
      { id: 4, name: '活力橙', gradient: 'linear-gradient(135deg, #ff8c00, #ff6347)' },
      { id: 5, name: '简约白', gradient: 'linear-gradient(135deg, #ffffff, #ffffff)' },
    ],
    selectedColorId: 0, // 默认选中高级黑
    watermarkText: '输入您的水印名称'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 处理水印输入
  onWatermarkInput(e) {
    this.setData({
      watermarkText: e.detail.value
    });
  },

  // 颜色选择处理
  onColorSelect(e) {
    const colorId = e.currentTarget.dataset.colorId;
    this.setData({
      selectedColorId: colorId
    });

    // 保存选中的颜色到云数据库（可选）
    const selectedColor = this.data.colorOptions[colorId];
    console.log('选中颜色:', selectedColor);
  },

  // 手机型号选择框点击
  onPhoneSelect(e) {
    const index = e.currentTarget.dataset.index;
    const phoneModels = this.data.phoneModels;
    phoneModels[index].selected = !phoneModels[index].selected;

    this.setData({
      phoneModels: phoneModels
    });

    console.log('手机型号选择状态:', phoneModels[index]);
  },

  // 条件选择器点击
  onConditionSelect(e) {
    const index = e.currentTarget.dataset.index;
    const phoneModels = this.data.phoneModels;

    // 切换下拉框显示状态
    phoneModels[index].showDropdown = !phoneModels[index].showDropdown;

    // 关闭其他下拉框
    phoneModels.forEach((item, i) => {
      if (i !== index) {
        item.showDropdown = false;
      }
    });

    this.setData({
      phoneModels: phoneModels
    });
  },

  // 选择条件选项
  onConditionOptionSelect(e) {
    const phoneIndex = e.currentTarget.dataset.phoneIndex;
    const optionIndex = e.currentTarget.dataset.optionIndex;
    const phoneModels = this.data.phoneModels;

    // 更新选中的条件
    phoneModels[phoneIndex].condition = this.data.conditionOptions[optionIndex];
    phoneModels[phoneIndex].showDropdown = false;

    this.setData({
      phoneModels: phoneModels
    });

    console.log('条件选择:', phoneModels[phoneIndex]);
  },

  // 个性化调整按钮点击
  onPersonalizeAdjust() {
    wx.navigateTo({
      url: '/pages/personalizeAdjust/personalizeAdjust'
    })
  },

  // 生成报价单按钮点击
  onGenerateQuote() {
    const { selectedColorId, colorOptions, watermarkText, phoneModels } = this.data;
    const selectedColor = colorOptions[selectedColorId];

    console.log('生成报价单参数:', {
      selectedColor,
      watermarkText,
      phoneModels
    });

    // 这里可以调用云函数生成报价单图片
  }
})
