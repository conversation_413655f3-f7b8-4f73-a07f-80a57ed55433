// pages/qutote/qutote.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    activeTab: 0, // 默认选中回收报价
    iphoneBrandList: [],
    androidBrandList: [],
    isPreviewing: false,
    currentImage: '',
    viewCount: 0,
    updateTime: '',
  },
  // 打开预览
  previewImage(e) {
    const {androidBrandList} = this.data
    const index = e.currentTarget.dataset.index
    this.setData({
      isPreviewing: true,
      currentImage: androidBrandList[index].no_watermark_photo,
      viewCount: androidBrandList[index].views, // 从数据中获取真实值
      updateTime: androidBrandList[index].updateTime.substring(0, 10), // 从数据中获取真实值
    });
    wx.cloud.callFunction({
      name: 'updateViews',
      data: {
        id: androidBrandList[index]._id
      }
    }).then(res => {
      androidBrandList[index].views = androidBrandList[index].views +1
      this.setData({
        androidBrandList
      })
    }).catch(err => {
      console.error('调用云函数失败：', err)
    })
  },
  
  // 关闭预览
  closePreview() {
    this.setData({ isPreviewing: false });
  },

  // 切换选项
  switchTab(e) {
    this.setData({
      activeTab: Number(e.currentTarget.dataset.tab)
    });
    this.changeQutote(e.currentTarget.dataset.tab)
  },

  // 切换报价查询数据
  changeQutote(e) {
    wx.cloud.callFunction({
      name: 'getRecallBySeries',
      success: res => {
        this.setData({
          iphoneBrandList: res.result.data.series0,
          androidBrandList: res.result.data.series1
        })
      },
      fail: err => {
        console.error('查询失败:', err)
      }
    })
  },

  // 跳转到报价详情页面
  goToQuoteDetail() {
    wx.navigateTo({
      url: '/pages/quoteDetail/quoteDetail'
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.changeQutote(0);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})