/* pages/address/address.wxss */
page {
  background-color: #f6f7f9;
}

.btn-bottom {
  width: 92%;
  height: 90rpx;
  background-color: #007aff;
  color: white;
  font-size: 32rpx;
  justify-content: center;
  display: flex;
  align-items: center;
  border-radius: 12rpx;
  position: fixed;
  left: 4%;
  right: 0;
  bottom: 12rpx;
  z-index: 10;
}

.address-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  margin: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.default-card {
  border-left: 8rpx solid #07C160;
}

.card-left {
  flex: 1;
  display: flex;
  align-items: flex-start;
}

.default-tag {
  background: #07C160;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 20rpx;
  margin-top: 6rpx;
}

.info-section {
  flex: 1;
}

.name-phone {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-right: 24rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.address {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.region {
  margin-right: 12rpx;
}

.detail {
  word-break: break-all;
}

.edit-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 20rpx;
}

.edit-btn image {
  width: 36rpx;
  height: 36rpx;
}