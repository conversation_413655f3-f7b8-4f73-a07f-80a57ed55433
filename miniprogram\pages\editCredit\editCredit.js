// pages/editCredit/editCredit.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    accountType: 'bank', // 账号类型: bank/alipay/wechat
    bankName: '', // 银行名称
    accountName: '', // 收款人姓名
    accountNumber: '', // 手机号/收款账号
    wechatQRCode: '', // 微信收款码图片
    isDefault: false, // 是否设为默认
    agreed: false, // 是否同意协议
    imageFileID: '', // 云存储 fileID
    id: '',
    imagePublicUrl: ''
  },


  // 账号类型变更
  handleAccountTypeChange(e) {
    this.setData({
      accountType: e.detail.value,
      wechatQRCode: ''
    })
  },

  // 银行名称输入
  handleAccountInput(e) {
    this.setData({
      bankName: e.detail.value
    })
  },

  // 收款人姓名输入
  handleNameInput(e) {
    this.setData({
      accountName: e.detail.value
    })
  },

  // 手机号输入
  handlePhoneInput(e) {
    this.setData({
      accountNumber: e.detail.value
    })
  },

  // 默认收款方式切换
  handleDefaultChange(e) {
    this.setData({
      isDefault: e.detail.value
    })
  },

  // 切换协议同意状态
  toggleAgreement() {
    this.setData({
      agreed: !this.data.agreed
    })
  },

  // 选择图片
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempPath = res.tempFilePaths[0];
        this.setData({
          wechatQRCode: tempPath
        })
        // 生成时间戳文件名 + 后缀
        const ext = tempPath.match(/\.\w+$/)[0];
        const cloudPath = `images/${Date.now()}${ext}`;

        // 上传到云存储
        wx.cloud.uploadFile({
          cloudPath,
          filePath: tempPath,
          config: {
            // 设置上传时的权限为公开可读
            env: app.globalData.env, // 替换为你的实际环境ID
            acl: {
              '*': ['read'] // 所有人可读
            }
          }
        }).then(uploadRes => {
          // 获取永久可访问的URL
          return wx.cloud.getTempFileURL({
            fileList: [uploadRes.fileID]
          });
        }).then(urlRes => {
          // 存储永久URL和FileID
          this.setData({
            imageFileID: urlRes.fileList[0].fileID,
            imagePublicUrl: urlRes.fileList[0].tempFileURL // 虽然是tempFileURL，但因为设置了公开权限，实际是永久有效的
          });
          console.log('上传成功，公开URL:', urlRes.fileList[0].tempFileURL);
        }).catch(err => {
          console.error('upload error', err);
        });
      }
    })
  },

  // 删除图片
  removeImage(e) {
    this.setData({
      wechatQRCode: '',
      imageFileID: ''
    })
  },

  // 阻止事件冒泡
  stopPropagation(e) {
    // 空方法，仅用于阻止冒泡
  },

  // 查看协议
  navigateToAgreement() {
    wx.navigateTo({
      url: '../protocol/protocol'
    })
  },

  // 提交表单
  handleSubmit() {
    const {
      accountType,
      bankName,
      accountName,
      accountNumber,
      wechatQRCode,
      isDefault,
      imageFileID,
      imagePublicUrl,
      agreed,
      id
    } = this.data

    if (!agreed) {
      return
    }

    // 表单验证
    if (accountType === 'bank' && !bankName) {
      wx.showToast({
        title: '请输入银行名称',
        icon: 'none'
      })
      return
    }

    if (accountType === 'bank' && bankName && !this.isPureChinese(bankName)) {
      wx.showToast({
        title: '请输入中文银行名称',
        icon: 'none'
      })
      return
    }

    if (!accountName) {
      wx.showToast({
        title: '请输入收款户名',
        icon: 'none'
      })
      return
    }

    if (accountName && !this.isPureChinese(accountName)) {
      wx.showToast({
        title: '请输入中文收款户名',
        icon: 'none'
      })
      return
    }

    if (!accountNumber) {
      wx.showToast({
        title: accountType === 'wechat' ? '请输入手机号' : '请输入收款账号',
        icon: 'none'
      })
      return
    }

    if (accountType === 'wechat' && accountNumber && !this.validatePhone(accountNumber)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return
    }

    if (accountType === 'alipay' && accountNumber && !this.isPhoneOrEmail(accountNumber)) {
      wx.showToast({
        title: '阿里账号应该是手机号或者邮箱号',
        icon: 'none'
      })
      return
    }

    if (accountType === 'wechat' && !wechatQRCode) {
      wx.showToast({
        title: '请上传微信收款码',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '保存中...',
    })

    // 网络请求
    const creditInfo = {
      accountType,
      bankName,
      accountName,
      accountNumber,
      imagePublicUrl,
      isDefault,
      id
    }
    wx.cloud.callFunction({
      name: 'updateCreditById',
      data: {
        creditInfo
      },
      success: res => {
        if (res.result.code === 200) {
          wx.showToast({
            title: '更新成功',
            duration: 1500, // 默认 1.5s
            icon: 'success',
            success: () => {
              wx.navigateBack(); // toast 消失后立即返回
            }
          });
        } else {
          wx.showToast({
            title: res.result.message,
            icon: 'none'
          })
        }
      },
      fail: err => {
        console.error('更新失败:', err)
        wx.showToast({
          title: '更新失败',
          icon: 'none'
        })
      }
    })
  },
  // 删除
  handleDelete(){
    wx.showLoading({
      title: '保存中...',
    })
    const {id}  = this.data;
    console.log(id);
    wx.cloud.callFunction({
      name: 'deleteCreditById',
      data: {
        id: id
      },
      success: res => {
        wx.hideLoading()
        if (res.result.code === 200) {
          wx.showToast({
            title: '删除成功',
            duration: 1500, // 默认 1.5s
            icon: 'success',
            success: () => {
              wx.navigateBack(); // toast 消失后立即返回
            }
          });
        } else {
          wx.showToast({
            title: res.result.message,
            icon: 'none'
          })
        }
      },
      fail: err => {
        console.error('删除失败:', err)
        wx.showToast({
          title: '删除失败',
          icon: 'none'
        })
      }
    })
  },
  // 手机号校验
  validatePhone(phone) {
    const reg = /^1[3-9]\d{9}$/
    return reg.test(phone)
  },
  // 中文校验
  isPureChinese(str) {
    return /^[\u4e00-\u9fa5]+$/.test(str);
  },
  // 邮箱校验
  isEmail(email) {
    return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email);
  },
  isPhoneOrEmail(input) {
    return this.validatePhone(input) || this.isEmail(input);
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    var that = this
    wx.showLoading({
      title: '加载中...',
    })
    wx.cloud.callFunction({
      name: 'getCreditById',
      data: {
        _id: options.id
      },
      success: res => {
        if (res.result.code === 200) {
          wx.hideLoading();
          // 处理查询结果
          that.setData({
            accountName: res.result.data.accountName,
            accountNumber: res.result.data.accountNumber,
            accountType: res.result.data.accountType,
            bankName: res.result.data.bankName,
            imageFileID: res.result.data.imageFileID,
            isDefault: res.result.data.isDefault,
            id: res.result.data._id,
            wechatQRCode: res.result.data.imageFileID
          })
        } else {
          wx.showToast({
            title: res.result.message,
            icon: 'none'
          })
        }
      },
      fail: err => {
        console.error('调用失败:', err)
        wx.showToast({
          title: '查询失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})