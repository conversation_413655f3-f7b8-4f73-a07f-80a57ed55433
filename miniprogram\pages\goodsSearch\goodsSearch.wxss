/* pages/goodsSearch/goodsSearch.wxss */
page {
  background-color: #f6f7f9;
}

.search-container {
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
}

.search-box {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.search-input {
  flex: 1;
  padding: 10px;
  font-size: 14px;
}

.placeholder-style {
  color: #999;
}

.search-btn {
  margin: 0;
  padding: 0 15px;
  height: 40px;
  line-height: 40px;
  background-color: #5677fc;
  color: white;
  border-radius: 0;
  font-size: 14px;
  border: none;
}

.search-btn::after {
  border: none;
}

.container {
  padding: 15rpx;
  box-sizing: border-box;
}

.goods-list {
  display: flex;
  flex-wrap: wrap;
}

.goods-item {
  width: 30%;
  margin: 10rpx;
  background: #fff;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

.goods-image {
  width: 100%;
  height: 200rpx;
  display: block;
}

.goods-info {
  padding: 15rpx;
  text-align: center;
}

.goods-name {
  font-size: 26rpx;
  color: #333;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.goods-price {
  font-size: 28rpx;
  color: #e64340;
  font-weight: bold;
  display: block;
  margin-top: 10rpx;
}

.no-more {
  width: 100%;
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 26rpx;
  margin-bottom: 150rpx;
}