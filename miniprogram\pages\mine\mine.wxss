/* pages/mine/mine.wxss */
page {
  background-color: #f7f7f7;
}

.box {
  width: 88%;
  background-color: #fff;
  position: relative;
  border-radius: 20rpx;
  display: flex;
  padding: 20rpx 10rpx;
  margin: 20rpx auto;
}

/* 筛选栏容器 */
.status-filter {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  background-color: #fff;
}

/* 左侧标题 */
.filter-title {
  font-size: 38rpx;
  color: rgb(0, 0, 0);
  font-weight: bold;
  padding: 10rpx 70rpx;
  width: 85rpx;
}

/* 中间分割线 */
.divider {
  width: 1rpx;
  height: 100%;
  background-color: #e0e0e0;
  margin: 0 20rpx;
}

/* 右侧按钮容器 */
.filter-buttons {
  display: flex;
  flex: 1;
  justify-content: flex-end;
}

/* 单个筛选按钮 */
.filter-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 57rpx;
}

/* 按钮图标 */
.button-icon {
  width: 55rpx;
  height: 55rpx;
  margin-bottom: 8rpx;
}

/* 按钮文字 */
.button-text {
  font-size: 25rpx;
  color: inherit;
  color: #a7a7a7;
}

/* 订单容器 */
.order-container {
  padding: 20rpx;
  width: 100%;
}

/* 订单标题 */
.order-title {
  font-size: 32rpx;
  font-weight: bold;
  color: rgb(116, 116, 116);
}

/* 选项卡容器 */
.order-tabs {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 20rpx 0;
}

/* 单个选项卡 */
.tab-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 10rpx;
  position: relative;
  flex-direction: column;
}

.cover-btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important; /* 强制覆盖 */
  height: 100%;
  opacity: 0;
}

/* 选项卡图标 */
.tab-icon {
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 15rpx;
}

/* 选项卡文字 */
.tab-text {
  font-size: 25rpx;
  color: inherit;
  color: #a7a7a7;
}