// pages/address/address.js
Page({
  data: {
    receiver: '', // 收货人
    phone: '', // 手机号码
    region: ["广东省", "深圳市", "福田区"], // 省市区
    address: '', // 详细地址
    customItem: '全部',
    regionName: '广东省, 深圳市, 福田区',
    type: '1',
    id: '',
  },

  onLoad(options) {
    if (options.id) {
      wx.showLoading({
        title: '加载中...',
        mask: true
      })
      wx.cloud.callFunction({
        name: 'getAddressBookById',
        data: {
          id: options.id
        },
        success: res => {
          wx.hideLoading()
          if (res.result.code === 200) {
            this.setData({
              receiver: res.result.data.consignee, // 收货人
              phone: res.result.data.phone, // 手机号码
              region: [res.result.data.province, res.result.data.city, res.result.data.district], // 省市区
              address: res.result.data.detail, // 详细地址
              regionName: res.result.data.province + ", " + res.result.data.city + ", " + res.result.data.district,
              id: options.id
            })
          } else {
            console.warn(res.result.message)
          }
        },
        fail: err => {
          console.error(err)
        }
      })
    }
    this.setData({
      type: options.type
    })
  },

  // 收货人输入
  onReceiverInput: function (e) {
    this.setData({
      receiver: e.detail.value
    });
  },

  // 手机号码输入
  onPhoneInput: function (e) {
    this.setData({
      phone: e.detail.value
    });
  },

  // 省市区选择
  onRegionChange: function (e) {
    this.setData({
      region: e.detail.value,
      regionName: e.detail.value.join(',')
    });
  },

  // 详细地址输入
  onAddressInput: function (e) {
    this.setData({
      address: e.detail.value
    });
  },

  // 保存地址
  onSave: function () {
    const {
      receiver,
      phone,
      region,
      address,
      regionName,
      type,
      id
    } = this.data;

    if (!receiver) {
      wx.showToast({
        title: '请输入收货人姓名',
        icon: 'none'
      });
      return;
    }

    if (!phone) {
      wx.showToast({
        title: '请输入手机号码',
        icon: 'none'
      });
      return;
    }

    if (!phone.match(/^1[3-9]\d{9}$/)) {
      wx.showToast({
        title: '请输入正确的手机号码',
        icon: 'none'
      });
      return;
    }

    if (!region || region.length === 0) {
      wx.showToast({
        title: '请选择省市区',
        icon: 'none'
      });
      return;
    }

    if (region && regionName.includes("全部")) {
      wx.showToast({
        title: '请选择正确的省市区',
        icon: 'none'
      });
      return;
    }

    if (!address) {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    if (id) {
      wx.cloud.callFunction({
        name: 'updateAddressBook',
        data: {
          id: id, // 必填
          consignee: receiver,
          phone: phone,
          province: region[0],
          city: region[1],
          district: region[2],
          detail: address,
          type
        },
        success: res => {
          if (res.result.code === 200) {
            wx.hideLoading()
            wx.navigateBack()
          } else {
            wx.showToast({
              title: res.result.message,
              icon: 'none'
            })
          }
        },
        fail: err => {
          console.error(err)
          wx.showToast({
            title: '更新失败',
            icon: 'none'
          })
        }
      })
    } else {
      wx.cloud.callFunction({
        name: 'addAddressBook',
        data: {
          type: type, // 如果用户已经有一个type=1的地址，这次调用会失败
          consignee: receiver,
          phone: phone,
          province: region[0],
          city: region[1],
          district: region[2],
          detail: address,
          is_default: true
        },
        success: res => {
          wx.hideLoading()
          if (res.result.code === 400) {
            wx.navigateBack();
            wx.showToast({
              title: res.result.message,
              icon: 'none'
            })
          } else {
            // 返回上一页
            wx.navigateBack();
          }
        },
        fail: err => {
          console.error(err)
        }
      })
    }

  },

  // 删除地址
  onDelete() {
    wx.cloud.callFunction({
      name: 'deleteAddressBook',
      data: {
        id: this.data.id
      },
      success: res => {
        if (res.result.code === 200) {
          wx.navigateBack()
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          })
        } else {
          wx.showToast({
            title: res.result.message,
            icon: 'none'
          })
        }
      },
      fail: err => {
        console.error(err)
        wx.showToast({
          title: '删除失败',
          icon: 'none'
        })
      }
    })
  }

});