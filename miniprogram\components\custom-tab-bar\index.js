const app = getApp()
Component({
  data: {
    selected: 0,
    icon: [
      "/images/tab_home.png",
      "", // 动态图
      "/images/tab_mine.png"
    ],
    selectedIcon: [
      "/images/tab_home_active.png",
      "", // 动态图
      "/images/tab_mine_active.png"
    ]
  },
  ready: function () {
    try {
      const app = getApp();
      const fileID = 'cloud://' + app.globalData.imgEnv + '/images/tab_goods.gif';
  
      // 确保有默认值
      if (app.globalData.selected === undefined) {
        app.globalData.selected = 0;
      }
  
      this.setData({
        icon: {
          ...this.data.icon,
          1: fileID
        },
        selectedIcon: {
          ...this.data.selectedIcon,
          1: fileID
        },
        selected: app.globalData.selected
      });
    } catch (error) {
      console.error('Component ready error:', error);
      this.setData({
        selected: 0
      });
    }
  },
  methods: {
    switchTab(e) {
      const path = e.currentTarget.dataset.path;
      const index = this.getIndex(path);
      app.globalData.selected = index;
      wx.reLaunch({
        url: '/' + path
      });
    },
    getIndex(path) {
      const pages = [
        "pages/index/index",
        "pages/goods/goods",
        "pages/mine/mine"
      ];;
      return pages.indexOf(path);
    }
  }
});