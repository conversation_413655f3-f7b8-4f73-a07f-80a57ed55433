// 云函数入口文件
const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const db = cloud.database()

  // 解构参数
  const { id } = event // 要查询的地址记录_id

  // 参数校验
  if (!id) {
    return {
      code: 400,
      message: '缺少地址ID参数'
    }
  }

  try {
    // 查询指定ID的地址，同时验证该地址属于当前用户
    const result = await db.collection('address_book')
      .where({
        _id: id,
        openid: wxContext.OPENID
      })
      .get()

    // 检查是否找到记录
    if (result.data.length === 0) {
      return {
        code: 404,
        message: '未找到该地址或无权访问'
      }
    }

    return {
      code: 200,
      message: '获取成功',
      data: result.data[0] // 返回单条地址数据
    }
  } catch (err) {
    console.error(err)
    return {
      code: 500,
      message: '查询地址失败',
      error: err
    }
  }
}