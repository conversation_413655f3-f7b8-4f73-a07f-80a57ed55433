// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init()
const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    // 查询是否存在hidden数据
    const result = await db.collection('hidden') // 请替换为实际的集合名称
      .get()
    
    // 如果有数据返回true，否则返回false
    return {
      exists: result.data.length > 0
    }
  } catch (err) {
    console.error('查询失败：', err)
    return {
      exists: false,
      error: err.message
    }
  }
}