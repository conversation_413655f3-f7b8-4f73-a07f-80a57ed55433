// pages/address/address.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    address: [],
    type: '1',
    loading: false
  },

  editAddressBook(e){
    wx.navigateTo({
      url: `../addAddress/addAddress?type=${this.data.type}&id=${e.currentTarget.dataset.id}`,
    })
  },

  goToAddAddress(){
    wx.navigateTo({
      url: `../addAddress/addAddress?type=${this.data.type}`,
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      type: options.type
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.setData({
      loading: true
    })
    wx.cloud.callFunction({
      name: 'getAddressBook',
      data: {
        type: this.data.type
      },
      success: res => {
        this.setData({
          type: this.data.type,
          address: res.result.data,
          loading: false
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})