<view style="height: 100%;">
  <swiper style="width: 100%;height: 500rpx;" indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" interval="{{interval}}" duration="{{duration}}" circular="{{circular}}">
    <block wx:for="{{imgUrls}}" wx:key="*this">
      <swiper-item>
        <image src="{{item}}" style="width: 100%;height: 100%;" mode="aspectFill" />
      </swiper-item>
    </block>
  </swiper>
</view>
<view class="notice-bar">
  <!-- 左侧喇叭图标 -->
  <image class="horn-icon" src="../../images/notin.png" mode="aspectFit"></image>

  <!-- 右侧滚动文字 -->
  <view class="scroll-container">
    <swiper class="text-swiper" vertical autoplay interval="3000" circular duration="500">
      <swiper-item wx:for="{{noticeList}}" wx:key="index">
        <view class="notice-text">{{item.info}}</view>
      </swiper-item>
    </swiper>
  </view>
</view>
<view class="box">
  <view class="store-container">
    <view class="store-header">
    <span style="background-color: #fe2702;color: white;padding: 5rpx 18rpx;margin-right: 15rpx;border-radius: 25rpx;font-size: 27rpx;">门店</span>
      <text class="store-location">安卓全系回收-全网独家无扣费</text>
    </view>

    <view class="store-contact">
    <image src="../../images/phone-icon.png" mode="" style="width: 40rpx;height: 40rpx;margin: 0 10rpx;"/>
      <text class="contact-name">{{info.name}}</text>
      <text class="contact-phone">{{info.phone}}</text>
      <text style="position: absolute;right: 30rpx;font-size: 25rpx;color: #fe2702;" bind:tap="makePhoneCall">拨打></text>
    </view>

    <view class="store-contact">
      <image src="../../images/wx-icon.png" mode="" style="width: 40rpx;height: 40rpx;margin: 0 10rpx;"/>
      <text class="contact-phone">{{info.phone}}</text>
      <text style="position: absolute;right: 30rpx;font-size: 25rpx;color: #fe2702;" bind:tap="copyText">复制></text>
    </view>

    <view class="store-address">
      <image src="../../images/address-icon.png" mode="" style="width: 40rpx;height: 40rpx;margin: 0 10rpx;"/>
      <text>{{info.address}}</text>
      <text style="position: absolute;right: 30rpx;font-size: 25rpx;color: #fe2702;" bind:tap="openMap">导航></text>
    </view>
  </view>
  <view class="btn-card">
    <view class="gradient-btn" bindtap="goToOrder">
      一键发货
    </view>
  </view>
  <view style="height:20rpx;width:100%;"></view>
</view>
<view class="box2">
  <view class="customer-service">
    <view class="line left-line"></view>
    <view class="text">官方客服</view>
    <view class="line right-line"></view>
  </view>
  <view class="kf-box">
    <view class="kf-card">
      <view style="border: 1rpx solid #b19780;width: 180rpx;height:180rpx;border-radius: 15rpx;display: flex;justify-content: center;align-items: center;">
        <view style="border: 10rpx solid #b19780;width: 80%;height:80%;border-radius: 15rpx;">
          <image style="width: 100%;height: 100%;" src="{{wxmID}}" mode="aspectFill" bindtap="showImage" data-src="{{wxmID}}" />
        </view>
      </view>
      <view style="text-align: center;font-size: 26rpx;color: #AFAFAF;margin: 10rpx 0;">回收客服</view>
    </view>
  </view>
</view>
<view class="box2">
  <view class="tab-container">
    <view class="tab-item" bindtap="goToQuoteDetail">
      回收报价
    </view>
  </view>
  <!-- 品牌展示容器 -->
  <view class="brand-container">
    <!-- 循环渲染品牌列表 -->
    <block wx:for="{{androidBrandList}}" wx:key="index">
      <view class="brand-item" bindtap="previewImage" data-index="{{index}}">
        <!-- 品牌图片 -->
        <image class="brand-image" src="{{item.image}}" mode="aspectFit"></image>
        <!-- 品牌名称 -->
        <view class="brand-name">{{item.name}}</view>
      </view>
    </block>
  </view>
  <view style="height: 50rpx;width: 100%;"></view>
</view>
<view class="box2">
  <view style="padding: 30rpx 40rpx;font-weight: 600;font-size: 33rpx;">服务保障</view>
 <!-- 容器 -->
<view class="container">
  <!-- 第一个卡片 -->
  <view class="card">
    <view class="icon-container">
      <image class="icon" src="/images/rule-icon.png" mode="aspectFit"></image>
    </view>
    <view class="title">专业</view>
    <view class="subtitle">每天第一手报价</view>
    <view class="description">行业领头羊</view>
  </view>

  <!-- 第二个卡片 -->
  <view class="card">
    <view class="icon-container">
      <image class="icon" src="/images/server-icon.png" mode="aspectFit"></image>
    </view>
    <view class="title">服务</view>
    <view class="subtitle">全国百家联盟</view>
    <view class="description">报价动态更新</view>
  </view>

  <!-- 第三个卡片 -->
  <view class="card">
    <view class="icon-container">
      <image class="icon" src="/images/temp-icon.png" mode="aspectFit"></image>
    </view>
    <view class="title">团队</view>
    <view class="subtitle">上百人专业团队</view>
    <view class="description">只为您服务</view>
  </view>
</view>
</view>
<view class="box2">
  <view style="padding: 30rpx 40rpx;font-weight: 600;font-size: 33rpx;">回收流程</view>
  <view style="display: flex;margin: 5rpx 10rpx;justify-content: center;align-items: center;">
    <view style="margin: 0 10rpx;text-align: center;">
      <image src="../../images/look-price.png" mode="aspectFill" style="height: 40rpx;width: 40rpx;" />
      <view style="font-size: 23rpx;color: #9e9e9e;">发货下单</view>
    </view>
    <image src="../../images/right.png" mode="aspectFill" style="width: 20rpx;height: 20rpx;margin-left: 10rpx;" />
    <image src="../../images/right.png" mode="aspectFill" style="width: 20rpx;height: 20rpx;margin-right: 10rpx;" />
    <view style="margin: 0 10rpx;text-align: center;">
      <image src="../../images/goHome-icon.png" mode="aspectFill" style="height: 40rpx;width: 40rpx;" />
      <view style="font-size: 23rpx;color: #9e9e9e;">上门/到店</view>
    </view>
    <image src="../../images/right.png" mode="aspectFill" style="width: 20rpx;height: 20rpx;margin-left: 10rpx;" />
    <image src="../../images/right.png" mode="aspectFill" style="width: 20rpx;height: 20rpx;margin-right: 10rpx;" />
    <view style="margin: 0 10rpx;text-align: center;">
      <image src="../../images/true-icon.png" mode="aspectFill" style="height: 45rpx;width: 45rpx;" />
      <view style="font-size: 23rpx;color: #9e9e9e;">确认报价</view>
    </view>
    <image src="../../images/right.png" mode="aspectFill" style="width: 20rpx;height: 20rpx;margin-left: 10rpx;" />
    <image src="../../images/right.png" mode="aspectFill" style="width: 20rpx;height: 20rpx;margin-right: 10rpx;" />
    <view style="margin: 0 10rpx;text-align: center;">
      <image src="../../images/back-money.png" mode="aspectFill" style="height: 40rpx;width: 40rpx;" />
      <view style="font-size: 23rpx;color: #9e9e9e;">成交收款</view>
    </view>
  </view>
  <view class="accordion-container">
    <view class="accordion-item" wx:for="{{accordionList}}" wx:key="index">
      <!-- 面板标题 -->
      <view class="accordion-header" bindtap="toggleAccordion" data-index="{{index}}">
        <text>Q：{{item.question}}</text>
        <image class="arrow-icon" src="{{item.isOpen ? '/images/arrow-up.png' : '/images/arrow-down.png'}}" mode="aspectFit"></image>
      </view>

      <!-- 面板内容 -->
      <view class="accordion-content" style="height: {{item.isOpen ? item.contentHeight + 'px' : '0'}};">
        <view class="content-inner">{{item.answer}}</view>
      </view>
    </view>
  </view>
</view>
<view style="height: 200rpx;width: 100%;"></view>
<custom-tab-bar></custom-tab-bar>

<!-- 自定义预览容器 -->
<scroll-view scroll-y style="height: 100vh;" wx:if="{{isPreviewing}}" class="preview-container">
  <!-- 关闭按钮 -->
  <view class="close-btn" bindtap="closePreview">×</view>

  <!-- 可缩放图片区域 -->
  <image src="{{currentImage}}" mode="widthFix" style="width: 100%;" />

  <view style="width: 100%;height: 100rpx;"></view>

  <!-- 底部信息栏（固定在底部） -->
  <view class="image-info" style="position: fixed; bottom: 0;">
    浏览量: {{viewCount}} | 更新时间: {{updateTime}}
  </view>
</scroll-view>