/* components/custom-modal/custom-modal.wxss */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.modal-content {
  width: 80%;
  background: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
}

.modal-header {
  text-align: center;
  font-size: 32rpx;
  margin-bottom: 40rpx;
}

.icon-container {
  display: flex;
  justify-content: space-around;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.icon-item image {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.icon-item text {
  font-size: 28rpx;
}