// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database();

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const {credit} = event;
  const {OPENID} = wxContext;
  
  try {
    // 1. 先检查用户是否已有收款账户
    const countRes = await db.collection('credit')
      .where({
        _openid: OPENID
      })
      .count()
    
    const hasExistingAccounts = countRes.total > 0;
    
    // 2. 确定是否设置为默认
    // 如果没有已有账户，或者用户明确设置为默认，则设为默认
    const shouldSetDefault = !hasExistingAccounts || credit.isDefault;
    
    // 3. 构造插入数据
    const dataToInsert = {
      accountType: credit.accountType,
      accountName: credit.accountName,
      accountNumber: credit.accountNumber,
      isDefault: shouldSetDefault, // 使用计算出的默认值
      imageFileID: credit.imagePublicUrl || null,
      _openid: OPENID,
      bankName: credit.bankName,
      createdTime: new Date(),
      updatedTime: new Date()
    }
    
    // 4. 插入新数据
    const result = await db.collection('credit').add({
      data: dataToInsert
    })

    // 5. 如果设置为默认，取消其他默认设置
    if (shouldSetDefault) {
      await db.collection('credit')
        .where({
          _openid: OPENID,
          isDefault: true,
          _id: db.command.neq(result._id) // 排除刚插入的这条
        })
        .update({
          data: {
            isDefault: false,
            updatedTime: new Date()
          }
        })
    }
    
    return {
      code: 200,
      message: '添加成功',
      data: {
        _id: result._id,
        isDefault: shouldSetDefault // 返回是否设置为默认
      }
    }
  } catch(err) {
    console.error('添加失败:', err)
    return {
      code: 500,
      message: '服务器错误',
      error: err
    }
  }
}