// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { OPENID } = wxContext
  const { creditInfo } = event // 获取前端传入的更新数据
  
  // 参数校验
  if (!creditInfo || !creditInfo.id) {
    return {
      code: 400,
      message: '缺少必要参数'
    }
  }

  try {
    // 1. 准备更新数据
    const updateData = {
      accountType: creditInfo.accountType,
      accountName: creditInfo.accountName,
      accountNumber: creditInfo.accountNumber,
      bankName: creditInfo.bankName || null,
      imageFileID: creditInfo.imageFileID || null,
      isDefault: creditInfo.isDefault || false,
      updatedTime: new Date()
    }

    // 2. 执行更新操作（确保只能更新自己的数据）
    const updateResult = await db.collection('credit')
      .where({
        _id: creditInfo.id,
        _openid: OPENID // 安全校验，只能修改自己的数据
      })
      .update({
        data: updateData
      })

    // 3. 检查是否成功更新
    if (updateResult.stats.updated === 0) {
      return {
        code: 404,
        message: '未找到指定的收款账户或无权修改'
      }
    }

    // 4. 如果设置为默认，需要取消其他账户的默认状态
    if (creditInfo.isDefault) {
      await db.collection('credit')
        .where({
          _openid: OPENID,
          isDefault: true,
          _id: db.command.neq(creditInfo.id) // 排除当前更新的这条
        })
        .update({
          data: {
            isDefault: false,
            updatedTime: new Date()
          }
        })
    }

    return {
      code: 200,
      message: '更新成功',
      data: {
        _id: creditInfo.id,
        updated: updateResult.stats.updated
      }
    }
  } catch (err) {
    console.error('更新失败:', err)
    return {
      code: 500,
      message: '服务器错误',
      error: err
    }
  }
}