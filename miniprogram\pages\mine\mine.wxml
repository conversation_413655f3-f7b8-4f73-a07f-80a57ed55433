<!--pages/mine/mine.wxml-->
<view style="height: 180rpx;width: 100%;text-align: center;line-height: 250rpx;background-color: black;color: white;">我的</view>
<view style="background: linear-gradient(to bottom,black, #564124);width: 100%;height: 440rpx;position: relative;">
  <view style="display: flex;color: white;">
    <image src="../../images/avatar.png" mode="aspectFill" style="width: 120rpx;height: 120rpx;background-color: white;border-radius: 50%;margin: 20rpx 30rpx 30rpx;" />
    <view style="padding: 30rpx 0 0 10rpx;" bind:tap="goToLogin">
      <view style="line-height: 60rpx;font-size: 40rpx;font-weight: 600;">{{phone?phone:'未登录'}}</view>
      <view style="font-size: 28rpx;">未绑定回收对接员</view>
    </view>
  </view>
  <view style="display: flex;color: white;margin: 10rpx auto 40rpx;">
    <view style="align-items: center;display: flex;flex-direction: column;margin: 10rpx 11%;">
      <view style="font-size: 47rpx;font-weight: 600;">{{orderCount}}</view>
      <view style="font-size: 28rpx;">寄件数</view>
    </view>
    <view style="align-items: center;display: flex;flex-direction: column;margin: 10rpx auto;">
      <view style="font-size: 47rpx;font-weight: 600;">0</view>
      <view style="font-size: 28rpx;">机器数</view>
    </view>
    <view style="align-items: center;display: flex;flex-direction: column;margin: 10rpx 11%;">
      <view style="font-size: 47rpx;font-weight: 600;">{{totalAmount}}</view>
      <view style="font-size: 28rpx;">总金额</view>
    </view>
  </view>
  <view style="background: linear-gradient(to right,#292f49, #484e67);width: 90%;height: 100rpx;bottom: 0;border-top-right-radius: 15rpx;border-top-left-radius: 15rpx; position: absolute;left: 5%;display: flex;align-items: center;">
    <image src="../../images/vip.png" mode="aspectFill" style="width: 65rpx;height: 65rpx;margin-left: 20rpx;" />
    <view style="color: #e3caa2;font-weight: 600;font-size: 35rpx;margin: 0 15rpx;">开通会员</view>
    <view style="color: #e3caa2;font-size: 25rpx;">限时享优惠开会员</view>
    <view style="width: 150rpx;height: 50%;background-color: #f4e6c3;text-align: center;margin-left: auto;margin-right: 20rpx;color: #484e67;border-radius: 30rpx;font-size: 28rpx;line-height: 47rpx;" bindtap="handleReceive">{{isReceived ? '已领取' : '领取'}}</view>
  </view>
</view>
<view class="box">
  <view style="display: flex;position: relative;height: 50rpx;width: 100%;align-items: center;padding: 0 15rpx;">
    <view style="font-size: 27rpx;font-weight: bold;">关注<text style="color: #d2a564;">公众号</text>,不错过订单通知,赶紧去关注吧~</view>
    <image src="../../images/right.png" mode="aspectFill" style="width: 30rpx;height: 30rpx;position: relative;margin-left: auto;" />
  </view>
</view>
<view class="box">
  <view class="status-filter">
    <!-- 左侧标题 -->
    <view class="filter-title">货物状态</view>

    <!-- 中间分割线 -->
    <view class="divider"></view>

    <!-- 右侧筛选按钮 -->
    <view class="filter-buttons">
      <view class="filter-button" bind:tap="goToOrder" data-status="0">
        <image class="button-icon" src="../../images/all-icon.png"></image>
        <text class="button-text">全部</text>
      </view>

      <view class="filter-button" bind:tap="goToOrder" data-status="1">
        <image class="button-icon" src="../../images/checking-icon.png"></image>
        <text class="button-text">验机中</text>
      </view>
    </view>
  </view>
</view>
<view class="box">
  <view class="order-container">
    <!-- 顶部标题 -->
    <view class="order-title">我买到的</view>
    <!-- 订单状态选项卡 -->
    <view class="order-tabs">
      <view class="tab-item" bind:tap="goToBuy" data-status="1">
        <image class="tab-icon" src="../../images/wait-pay.png"></image>
        <text class="tab-text">待付款</text>
      </view>
      <view class="tab-item" bind:tap="goToBuy" data-status="2">
        <image class="tab-icon" src="../../images/wait-shouhuo.png"></image>
        <text class="tab-text">待收货</text>
      </view>
      <view class="tab-item" bind:tap="goToBuy" data-status="4">
        <image class="tab-icon" src="../../images/finsh.png"></image>
        <text class="tab-text">已完成</text>
      </view>
      <view class="tab-item" bind:tap="goToBuy" data-status="5">
        <image class="tab-icon" src="../../images/after-sales.png"></image>
        <text class="tab-text">退货/售后</text>
      </view>
    </view>
  </view>
</view>
<view class="box">
  <view class="order-container">
    <!-- 顶部标题 -->
    <view class="order-title">我卖出的</view>
    <!-- 订单状态选项卡 -->
    <view class="order-tabs">
      <view class="tab-item" bind:tap="goToOrder" data-status="2">
        <image class="tab-icon" src="../../images/wait-comfirm.png"></image>
        <text class="tab-text">待确认</text>
      </view>
      <view class="tab-item" bind:tap="goToOrder" data-status="3">
        <image class="tab-icon" src="../../images/settled.png"></image>
        <text class="tab-text">待结算</text>
      </view>
      <view class="tab-item" bind:tap="goToOrder" data-status="4">
        <image class="tab-icon" src="../../images/success.png"></image>
        <text class="tab-text">已完成</text>
      </view>
      <view class="tab-item" bind:tap="goToOrder" data-status="5">
        <image class="tab-icon" src="../../images/back-good.png"></image>
        <text class="tab-text">退货</text>
      </view>
    </view>
  </view>
</view>
<view class="box">
  <view class="order-container">
    <!-- 顶部标题 -->
    <view class="order-title">我的服务</view>
    <view class="order-tabs">
      <view class="tab-item" bindtap="showModal">
        <image class="tab-icon" src="../../images/address.png"></image>
        <text class="tab-text">售货地址</text>
      </view>
      <view class="tab-item" bind:tap="goToMustKnow">
        <image class="tab-icon" src="../../images/must-know.png"></image>
        <text class="tab-text">交易须知</text>
      </view>
      <view class="tab-item" bindtap="contactCustomerService">
        <image class="tab-icon" src="../../images/customer.png"></image>
        <text class="tab-text">联系客服</text>
        <!-- 透明按钮覆盖 -->
        <button open-type="contact" class="cover-btn"></button>
      </view>
      <view class="tab-item" bind:tap="goToIdCard">
        <image class="tab-icon" src="../../images/id-card.png"></image>
        <text class="tab-text">证件更新</text>
      </view>
      <view style="width: 100%;height: 25rpx;"></view>
      <view class="tab-item" bind:tap="goToLogin">
        <image class="tab-icon" src="../../images/phone.png"></image>
        <text class="tab-text">换绑号码</text>
      </view>
      <view class="tab-item" bind:tap="goToLogin">
        <image class="tab-icon" src="../../images/change.png"></image>
        <text class="tab-text">切换账号</text>
      </view>
      <view class="tab-item" bind:tap="goToCredit">
        <image class="tab-icon" src="../../images/collection.png"></image>
        <text class="tab-text">收款账号</text>
      </view>
      <view class="tab-item" bind:tap="goToQutote">
        <image class="tab-icon" src="../../images/no-watermark.png"></image>
        <text class="tab-text">无水印报价</text>
      </view>
    </view>
  </view>
</view>
<custom-modal showModal="{{showModal}}" bind:hideModal="hideModal" bind:selectAddress="selectAddress" bind:selectReturn="selectReturn" />
<view style="width: 100%;height: 230rpx;"></view>
<custom-tab-bar></custom-tab-bar>