.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  height: 100vh;
  background-color: #f8f8f8;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 80rpx;
  margin-top: 100rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #999;
}

.input-container {
  width: 80%;
  margin-bottom: 60rpx;
}

.input-group {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
}

.prefix {
  font-size: 32rpx;
  color: #333;
  margin-right: 20rpx;
}

.input {
  flex: 1;
  font-size: 32rpx;
  height: 50rpx;
}

.placeholder {
  color: #ccc;
}

.underline {
  height: 2rpx;
  background-color: #eee;
  width: 100%;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  line-height: 60rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  color: #fff;
  background-color: #ccc;
  margin-bottom: 40rpx;
  transition: all 0.3s;
}

.login-btn.active {
  background-color: #07c160;
}

/* 新增：协议勾选框样式 */
.agreement-check {
  display: flex;
  align-items: center;
  width: 100%;
  font-size: 24rpx;
  color: #666;
  margin-top: 20rpx;
}

.agreement-text {
  margin-left: 10rpx;
  line-height: 1.5;
}

.link {
  color: #576b95;
  margin: 0 5rpx;
  text-decoration: underline;
}