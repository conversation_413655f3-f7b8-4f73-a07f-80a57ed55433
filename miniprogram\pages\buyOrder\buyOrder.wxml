<!-- pages/order/order.wxml -->
<view class="container">

  <!-- 订单状态选择器 -->
  <view class="status-container">
    <scroll-view scroll-x class="status-scroll">
      <view wx:for="{{statusList}}" wx:key="value" class="status-item {{activeStatus === item.value ? 'active' : ''}}" bindtap="changeStatus" data-status="{{item.value}}">
        {{item.label}}
      </view>
    </scroll-view>
  </view>
</view>
<view class="page-container">
  <!-- 加载更多提示 -->
  <view wx:if="{{isLoading}}" class="loading-text">加载中...</view>
  <!-- 空显示 -->
  <view style="width: 90%;margin: 25% auto;text-align: center;" wx:if="{{!isLoading}}">
    <image src="../../images/null.png" mode="widthFix" style="width: 80%;" />
    <view style="color: #515151;margin-top: 50rpx;font-size: 30rpx;">没有找到此状态的订单</view>
  </view>
</view>