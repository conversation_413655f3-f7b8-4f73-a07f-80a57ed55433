const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    noticeList: [],
    activeTab: 0, // 默认选中回收报价
    iphoneBrandList: [],
    androidBrandList: [],
    accordionList: [{
        title: "Q: 以什么价格结算",
        content: "到货当天价格",
        isOpen: false,
        contentHeight: 0
      },
      {
        title: "Q: 什么时间结算",
        content: "质检处结果之后，第二天下午两点之前必须确认订单，否则订单会自动确认并打款",
        isOpen: false,
        contentHeight: 0
      }
    ],
    wxmID: "https://636c-cloud1-7ghpmzpt021a0d05-1373838248.tcb.qcloud.la/images/ewm.jpg",
    info: {
      name: '慕非',
      phone: '15323818868',
      address: '深圳市福田区锦峰大厦A座18F'
    },
    isPreviewing: false,
    currentImage: '',
    viewCount: 0,
    updateTime: '',
    imgUrls: [
      'https://636c-cloud1-7ghpmzpt021a0d05-1373838248.tcb.qcloud.la/images/index_top.jpg',
      'https://636c-cloud1-7ghpmzpt021a0d05-1373838248.tcb.qcloud.la/images/index_top_2.jpg'
    ],
    indicatorDots: true,  // 是否显示面板指示点
    autoplay: true,       // 是否自动切换
    interval: 3000,       // 自动切换时间间隔（ms）
    duration: 500,        // 滑动动画时长（ms）
    circular: true        // 是否采用衔接滑动
  },

  // 拨打电话
  makePhoneCall() {
    wx.makePhoneCall({
      phoneNumber: this.data.info.phone,
      success: () => console.log("拨号成功"),
      fail: (err) => console.log("拨号失败", err)
    })
  },

  // 展示客服图片
  showImage(e){
    wx.previewImage({
      current: e.currentTarget.dataset.src, 
      urls: [e.currentTarget.dataset.src]
    })
  },

  // 打开预览
  previewImage(e) {
    const {androidBrandList} = this.data
    const index = e.currentTarget.dataset.index
    this.setData({
      isPreviewing: true,
      currentImage: androidBrandList[index].watermark_photo,
      viewCount: androidBrandList[index].views, // 从数据中获取真实值
      updateTime: androidBrandList[index].updateTime.substring(0, 10), // 从数据中获取真实值
    });
    wx.cloud.callFunction({
      name: 'updateViews',
      data: {
        id: androidBrandList[index]._id
      }
    }).then(res => {
      androidBrandList[index].views = androidBrandList[index].views +1
      this.setData({
        androidBrandList
      })
    }).catch(err => {
      console.error('调用云函数失败：', err)
    })
  },
  
  // 关闭预览
  closePreview() {
    this.setData({ isPreviewing: false });
  },

  /**
   * 点击下单
   */
  goToOrder() {
    const phone = wx.getStorageSync('phone');
    if (phone) {
      wx.navigateTo({
        url: '../order/order',
      })
    } else {
      wx.navigateTo({
        url: '../login/login',
      })
    }
  },

  /**
   * 点击复制地址
   */
  copyText: function (e) {
    const text = `收货人: ${this.data.info.name}\n手机号: ${this.data.info.phone}\n地址: ${this.data.info.address}`;
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '内容已复制',
          icon: 'success'
        })
      }
    })
  },

  // 打开地图导航
  openMap() {
    wx.openLocation({
      latitude: 22.535967, // 替换为实际纬度
      longitude: 114.095935, // 替换为实际经度
      name: this.data.info.address,
      address: this.data.info.address,
      scale: 18
    })
  },

  // 计算内容高度
  calculateContentHeight() {
    const query = wx.createSelectorQuery().in(this);
    query.selectAll('.content-inner').boundingClientRect();

    query.exec(res => {
      const accordionList = this.data.accordionList.map((item, index) => {
        return {
          ...item,
          isOpen: false,
          contentHeight: res[0][index].height
        };
      });

      this.setData({
        accordionList
      });
    });
  },

  // 切换折叠状态
  toggleAccordion(e) {
    const index = e.currentTarget.dataset.index;
    const accordionList = this.data.accordionList.map((item, i) => {
      return {
        ...item,
        isOpen: i === index ? !item.isOpen : false // 点击当前项切换，其他项关闭
      };
    });

    this.setData({
      accordionList
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    wx.cloud.callFunction({
      name: 'getProcess',
      success: res => {
        this.setData({
          accordionList: res.result.data
        })
        this.calculateContentHeight();
      },
      fail: err => {
        console.error('获取失败:', err)
      }
    })
    wx.cloud.callFunction({
      name: 'getRecallBySeries',
      success: res => {
        this.setData({
          iphoneBrandList: res.result.data.series0,
          androidBrandList: res.result.data.series1
        })
      },
      fail: err => {
        console.error('查询失败:', err)
      }
    })
    wx.cloud.callFunction({
      name: 'getNotice',
      success: res => {
        this.setData({
          noticeList: res.result.data
        })
      },
      fail: err => {
        console.error('获取公告失败:', err)
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },

  // 跳转到报价详情页面
  goToQuoteDetail() {
    wx.navigateTo({
      url: '/pages/quoteDetail/quoteDetail'
    })
  }
})