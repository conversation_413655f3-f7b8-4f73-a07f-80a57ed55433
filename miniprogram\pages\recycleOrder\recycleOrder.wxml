<!-- pages/order/order.wxml -->
<view class="container">
  <!-- 搜索区域 -->
  <view class="search-container">
    <input class="search-input" placeholder="订单号/物流单号" placeholder-class="placeholder-style" bindinput="handleSearchInput" />
    <view class="search-btn" bindtap="handleSearch">搜索</view>
  </view>

  <!-- 订单状态选择器 -->
  <view class="status-container">
    <scroll-view scroll-x class="status-scroll">
      <view wx:for="{{statusList}}" wx:key="value" class="status-item {{activeStatus === item.value ? 'active' : ''}}" bindtap="changeStatus" data-status="{{item.value}}">
        {{item.label}}
      </view>
    </scroll-view>
  </view>
</view>
<view class="page-container">
  <!-- 订单列表 -->
  <scroll-view scroll-y class="order-scroll" enable-back-to-top bindscrolltolower="handleMore"
  lower-threshold="50">
    <view wx:for="{{orderList}}" wx:key="_id" class="order-card" hidden="{{item.isDelete}}">
      <!-- 订单基本信息 -->
      <view class="order-header">
        <text class="order-id">订单号：{{item.orderNo}}</text>
        <text class="order-time">{{item.createdTime}}</text>
      </view>

      <!-- 订单详情 -->
      <view class="order-body">
        <view class="info-row">
          <text class="info-label">数量:</text>
          <text class="info-value">{{item.quantity}}</text>
        </view>

        <view class="info-row">
          <text class="info-label">备注:</text>
          <text class="info-value">{{item.remark || '无'}}</text>
        </view>

        <view class="info-row">
          <text class="info-label">快递公司:</text>
          <text class="info-value">{{item.selectedCompany}}</text>
        </view>

        <view class="info-row">
          <text class="info-label">物流单号:</text>
          <text class="info-value">{{item.waybillNumber}}</text>
        </view>
        
        <view class="info-row">
          <text class="info-label">收款方式:</text>
          <text class="info-value" wx:if="{{item.creditInfo.accountType == 'alipay'}}">支付宝\n{{item.creditInfo.accountName}}\n{{item.creditInfo.accountNumber}}</text>
          <text class="info-value" wx:if="{{item.creditInfo.accountType == 'bank'}}">银行卡\n{{item.creditInfo.bankName}}\n{{item.creditInfo.accountName}}\n{{item.creditInfo.accountNumber}}</text>
          <text class="info-value" wx:if="{{item.creditInfo.accountType == 'wechat'}}">微信收款码\n{{item.creditInfo.accountName}}\n{{item.creditInfo.accountNumber}}</text>
          <image wx:if="{{item.creditInfo.accountType == 'wechat'}}" src="{{item.creditInfo.imageFileID}}" mode="aspectFill" style="width: 100rpx;height: 100rpx;" bindtap="previewImage" data-src="{{item.creditInfo.imageFileID}}"/>
        </view>
      </view>

      <!-- 备注图片 -->
      <view wx:if="{{item.imageFileID}}" class="image-container">
        <image class="remark-image" src="{{item.imageFileID}}" mode="aspectFill" bindtap="previewImage" data-src="{{item.imageFileID}}"></image>
      </view>

      <view class="info-row" wx:if="{{item.amount}}">
          <text class="info-label">回收报价:</text>
          <text class="info-value" style="color: #F56C6C;">￥<text style="font-size: large;">{{item.amount}}</text></text>
        </view>

      <!-- 订单操作 -->
      <view class="order-footer">
        <view style="font-size: 25rpx;color: #666;">订单状态：{{statusOptions[item.status]}}</view>
        <view class="recall-btn" bindtap="handleReback" data-id="{{item._id}}" wx:if="{{item.status==2}}" data-index="{{index}}">退回</view>
        <view class="comfirm-btn" bindtap="handleConfirmQuote" data-id="{{item._id}}" wx:if="{{item.status==2}}" data-index="{{index}}">同意报价</view>
        <view class="delete-btn" bindtap="handleDelete" data-id="{{item._id}}" wx:if="{{item.status==0||item.status==4||item.status==5}}" data-index="{{index}}">删除</view>
      </view>
    </view>

    <!-- 加载更多提示 -->
    <view wx:if="{{isLoading}}" class="loading-text">加载中...</view>
    <view wx:if="{{!hasMore}}" class="loading-text">没有更多了</view>
  </scroll-view>
</view>