<view style="width: 90%;margin: 40% auto;text-align: center;" wx:if="{{creditList.length<=0}}">
  <image src="../../images/null.png" mode="widthFix" style="width: 80%;" />
  <view style="color: #515151;margin-top: 50rpx;font-size: 30rpx;">您还没有收款方式，点击下方新增吧</view>
</view>

<!-- 账号列表 -->
<view class="account-list" wx:for="{{creditList}}" wx:key="_id" wx:if="{{creditList.length>0}}" bind:tap="handleChoseCredit" data-index="{{index}}">
  <!-- 支付宝账号 -->
  <view class="account-card alipay" wx:if="{{item.accountType === 'alipay'}}">
    <view class="account-header">
      <view class="account-info">
        <text class="account-type">账号类型：支付宝</text>
        <view class="default-tag" wx:if="{{item.isDefault}}">默认</view>
      </view>
      <view class="edit-btn" catch:tap="onEdit" data-id="{{item._id}}" data-type="alipay">
        <text>编辑</text>
      </view>
    </view>
    <view class="account-detail">
      <text class="detail-item">收款户名：{{item.accountName}}</text>
      <text class="detail-item">收款账号：{{item.accountNumber}}</text>
    </view>
  </view>

  <!-- 银行卡账号 -->
  <view class="account-card bank" wx:if="{{item.accountType === 'bank'}}">
    <view class="account-header">
      <view class="account-info">
        <text class="account-type">账号类型：银行卡</text>
        <view class="default-tag" wx:if="{{item.isDefault}}">默认</view>
      </view>
      <view class="edit-btn" catch:tap="onEdit" data-id="{{item._id}}" data-type="bank">
        <text>编辑</text>
      </view>
    </view>
    <view class="account-detail">
      <text class="detail-item">银行名称：{{item.bankName}}</text>
      <text class="detail-item">收款户名：{{item.accountName}}</text>
      <text class="detail-item">收款账号：{{item.accountNumber}}</text>
    </view>
  </view>

  <!-- 微信收款码 -->
  <view class="account-card wechat" wx:if="{{item.accountType === 'wechat'}}">
    <view class="account-header">
      <view class="account-info">
        <text class="account-type">账号类型：微信收款码</text>
        <view class="default-tag" wx:if="{{item.isDefault}}">默认</view>
      </view>
      <view class="edit-btn" catch:tap="onEdit" data-id="{{item._id}}" data-type="wechat">
        <text>编辑</text>
      </view>
    </view>
    <view class="account-detail">
      <text class="detail-item">收款户名：{{item.accountName}}</text>
      <text class="detail-item">收款账号：{{item.accountNumber}}</text>
      <image class="qr-code" src="{{item.imageFileID}}"></image>
    </view>
  </view>
</view>
<view style="width: 100%;height: 150rpx;"></view>
<view class="btn-bottom" bind:tap="goToAddCredit">+ 新增收款方式</view>