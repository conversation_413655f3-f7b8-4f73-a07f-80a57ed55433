/* pages/address/address.wxss */
page {
  background-color: #f6f7f9;
}

.container {
  padding: 30rpx 35rpx 0rpx;
  background-color: #fff;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}

.label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
}

.input {
  flex: 1;
  font-size: 28rpx;
}

.picker {
  flex: 1;
  font-size: 28rpx;
  color: rgb(90, 89, 89);
}

.save-btn {
  border-radius: 50rpx;
  padding: 25rpx 0;
  width: 85%;
  background-color: rgb(0, 98, 255);
  color: white;
  text-align: center;
  margin: 30rpx auto 0rpx;
}

.delete-btn {
  border-radius: 50rpx;
  padding: 25rpx 0;
  width: 85%;
  background-color: rgb(255, 13, 13);
  color: white;
  text-align: center;
  margin: 30rpx auto 0rpx;
}