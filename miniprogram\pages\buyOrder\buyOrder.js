// pages/order/order.js
Page({
  data: {
    activeStatus: 0, // 当前选中状态
    statusList: [{
        label: '全部',
        value: 0
      },
      {
        label: '待付款',
        value: 1
      },
      {
        label: '待发货',
        value: 2
      },
      {
        label: '待收货',
        value: 3
      },
      {
        label: '已完成',
        value: 4
      },
      {
        label: '退货/售后',
        value: 5
      }
    ],
    orderList: [], // 订单列表数据
    isLoading: false,
  },

  onLoad(options) {
    console.log(options);
    if(options.status){
      this.setData({
        activeStatus: Number(options.status)
      })
    }
    // 加载初始数据
    this.loadOrderList();
  },

  // 切换订单状态
  changeStatus(e) {
    const status = e.currentTarget.dataset.status;
    if (status != this.data.activeStatus) {
      this.setData({
        activeStatus: status,
      }, () => {
        this.loadOrderList();
      });
    }
  },

  // 加载订单数据
  loadOrderList() {
    if (this.data.isLoading) return;

    this.setData({
      isLoading: true
    });

    var that = this;

    // 设置一个延时执行的定时器
    setTimeout(() => {
      that.setData({
        isLoading: false
      })
    }, 1000);
  },
});