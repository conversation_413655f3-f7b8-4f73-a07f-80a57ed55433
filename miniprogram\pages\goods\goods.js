// pages/goods/goods.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    goodsList: [],
    fileID: '',
    iphoneImg: '',
    andriodImg: '',
    ipadImg: '',
    computerImg: '',
    wxmID: ''
  },

  // 点击商品
  handleClick(){
    wx.showModal({
      title: '提示',
      content: '请联系客服获取更多详情',
      success: (res) => {
        if (res.confirm) {
          wx.previewImage({
            current: this.data.wxmID,
            urls: [this.data.wxmID]
          })
        }
      }
    })
  },

  // 查询
  handleSearch(){
    wx.navigateTo({
      url: '../goodsSearch/goodsSearch',
    })
  },

  // 查询品牌
  handleSearchBySeries(e){
    wx.cloud.callFunction({
      name: 'getGoods',
      data: {
        series: e.currentTarget.dataset.series // 查询series为1的商品
      },
      success: res => {
        this.setData({
          goodsList: res.result.data
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const fileID = 'cloud://' + app.globalData.imgEnv + '/images/good_top.png';
    const iphoneImg = 'cloud://' + app.globalData.imgEnv + '/images/pc01.png';
    const andriodImg = 'cloud://' + app.globalData.imgEnv + '/images/pc02.png';
    const computerImg = 'cloud://' + app.globalData.imgEnv + '/images/pc04.png';
    const ipadImg = 'cloud://' + app.globalData.imgEnv + '/images/pc03.png';
    const wxmID = 'cloud://' + app.globalData.imgEnv + '/images/ewm.jpg';
    this.setData({
      fileID,
      iphoneImg,
      andriodImg,
      computerImg,
      ipadImg,
      wxmID
    })
    wx.cloud.callFunction({
      name: 'getGoods',
      data: {}, // 不传参数则返回所有商品
      success: res => {
        this.setData({
          goodsList: res.result.data
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})