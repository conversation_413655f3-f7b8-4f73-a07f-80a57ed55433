.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 999;
}

.tab-bar-item, .tab-bar-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  flex: 1;
}

.tab-bar-item .tab-img {
  width: 48rpx;
  height: 48rpx;
}

.tab-bar-center {
  position: relative;
  margin-top: -30rpx;
}

.circle-bg {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.center-icon {
  width: 70rpx;
  height: 70rpx;
}

.tab-bar-item .tab-text {
  font-size: 22rpx;
  color: #999;
  margin-top: 6rpx;
}

.tab-bar-center .tab-text {
  font-size: 22rpx;
  color: #999;
  margin-top: 6rpx;
}

.selected {
  color: #000000;
}