// 云函数入口文件
const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const db = cloud.database()
  const _ = db.command

  // 解构参数
  const { id } = event

  // 参数校验
  if (!id) {
    return {
      code: 400,
      message: '缺少地址ID参数'
    }
  }

  try {
    // 1. 先验证该地址属于当前用户
    const checkResult = await db.collection('address_book')
      .where({
        _id: id,
        openid: wxContext.OPENID
      })
      .count()

    if (checkResult.total === 0) {
      return {
        code: 404,
        message: '未找到该地址或无权操作'
      }
    }

    // 2. 执行删除
    const deleteResult = await db.collection('address_book')
      .doc(id)
      .remove()

    return {
      code: 200,
      message: '删除成功',
      data: deleteResult
    }
  } catch (err) {
    console.error(err)
    
    // 处理特定错误
    if (err.errCode === 'DATABASE_PERMISSION_DENIED') {
      return {
        code: 403,
        message: '无权操作该地址'
      }
    }

    return {
      code: 500,
      message: '删除地址失败',
      error: err.message
    }
  }
}