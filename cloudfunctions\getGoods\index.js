// 云函数入口文件
const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    const { series, searchText } = event
    const db = cloud.database()
    const _ = db.command

    // 构建查询条件
    let queryCondition = {}
    
    // 按series筛选
    if (series !== undefined && series !== '') {
      queryCondition.series = series
    }
    
    // 按name模糊搜索
    if (searchText && searchText.trim() !== '') {
      queryCondition.name = db.RegExp({
        regexp: searchText,
        options: 'i' // 不区分大小写
      })
    }

    // 查询数据
    const result = await db.collection('goods')
      .where(queryCondition)
      .orderBy('createTime', 'desc')
      .get()

    return {
      code: 0,
      data: result.data,
      message: '查询成功'
    }
  } catch (err) {
    console.error(err)
    return {
      code: -1,
      data: null,
      message: '查询失败: ' + err.message
    }
  }
}