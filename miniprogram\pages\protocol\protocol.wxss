/* pages/protocol/protocol.wxss */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #fff;
}

.header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.content {
  width: 90%;
  margin: 0 auto;
  flex: 1;
  color: #666;
  line-height: 1.6;
  padding-bottom: 100rpx;
}

.text-content {
  font-size: 28rpx;
  white-space: pre-line;
}