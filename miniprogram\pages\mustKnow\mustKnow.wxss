.container {
  padding: 20rpx;
  background-color: #f7f7f7;
}

.header {
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  margin: 20rpx 0;
  color: #333;
}

.section {
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
  border-left: 8rpx solid #07c160;
  padding-left: 15rpx;
}

.content-box {
  padding: 10rpx;
}

.content-item {
  display: flex;
  margin-bottom: 20rpx;
  line-height: 1.6;
}

.item-number {
  font-weight: bold;
  margin-right: 10rpx;
}

.item-text {
  flex: 1;
  font-size: 28rpx;
  color: #666;
}

.footer {
  margin: 40rpx 0;
  padding: 0 20rpx;
}

.confirm-btn {
  background-color: #07c160;
  color: white;
  border-radius: 50rpx;
  font-size: 32rpx;
}