/* pages/personalizeAdjust/personalizeAdjust.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
}

.nav-left {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 48rpx;
  color: #333333;
  font-weight: 300;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.more-icon,
.record-icon {
  font-size: 32rpx;
  color: #333333;
  margin-left: 20rpx;
}

/* 标签页 */
.tabs-container {
  background-color: #ffffff;
  padding: 0 30rpx;
}

.tabs {
  display: flex;
  border-bottom: 1rpx solid #e5e5e5;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666666;
  position: relative;
}

.tab-item.active {
  color: #333333;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #333333;
}

/* 选择型号 */
.model-selector {
  display: flex;
  align-items: center;
}

.selector-label {
  font-size: 28rpx;
  color: #202124;
  white-space: nowrap;
  padding: 0 10rpx;
}

.selector-input {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 2rpx solid #e8eaed;
  position: relative;
}

.selector-input::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(250, 251, 252, 0.8) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.selector-input:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
  border-color: #4285f4;
}

.selector-input:active::before {
  opacity: 1;
}

.selector-input .placeholder {
  color: #9aa0a6;
  font-size: 32rpx;
  font-weight: 400;
  letter-spacing: 0.5rpx;
}

.selector-input .selected {
  color: #202124;
  font-size: 32rpx;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

.dropdown-icon {
  font-size: 28rpx;
  color: #5f6368;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.selector-input:active .dropdown-icon {
  transform: rotate(180deg);
  color: #4285f4;
}

/* 空状态 */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 60rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-icon image {
  width: 100%;
  height: 100%;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  text-align: center;
}

/* 底部按钮 */
.bottom-buttons {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #e5e5e5;
}

.btn-secondary {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 32rpx;
  color: #666666;
  background-color: #f5f5f5;
  border-radius: 44rpx;
}

.btn-primary {
  flex: 2;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 32rpx;
  color: #ffffff;
  background-color: #333333;
  border-radius: 44rpx;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  width: 600rpx;
  max-height: 800rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 40rpx;
  color: #999999;
}

.modal-body {
  max-height: 600rpx;
  overflow-y: auto;
}

.model-item {
  padding: 30rpx;
  font-size: 28rpx;
  color: #333333;
  border-bottom: 1rpx solid #f5f5f5;
}

.model-item:last-child {
  border-bottom: none;
}

.model-item:active {
  background-color: #f5f5f5;
}
