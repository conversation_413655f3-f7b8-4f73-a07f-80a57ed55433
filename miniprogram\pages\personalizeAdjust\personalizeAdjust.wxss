page{
  background: #f5f5f5;
}

.container {
  background: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 手机类型切换 */
.type-tabs {
  display: flex;
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 15rpx;
  overflow: hidden;
}

.type-tab {
  flex: 1;
  padding: 20rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  background: #f0f0f0;
  transition: all 0.3s;
}

.type-tab.active {
  background: #333;
  color: #fff;
}

/* 选择型号 */
.model-selector {
  margin: 20rpx 30rpx;
  background: #fff;
  border-radius: 15rpx;
  padding: 30rpx;
}

.selector-label {
  font-size: 32rpx;
  color: #333;
  margin-right: 20rpx;
}

.model-picker {
  margin-top: 20rpx;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 10rpx;
  border: 1rpx solid #e0e0e0;
}

.picker-text {
  font-size: 28rpx;
  color: #666;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 多选按钮 */
.multi-select-btn {
  margin-top: 20rpx;
  padding: 16rpx 20rpx;
  background: #007aff;
  color: #fff;
  border-radius: 8rpx;
  text-align: center;
  font-size: 28rpx;
}

/* 已选择的型号显示 */
.selected-models {
  margin-top: 20rpx;
}

.selected-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.selected-tag {
  display: flex;
  align-items: center;
  background: #333;
  color: #fff;
  padding: 8rpx 12rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.tag-text {
  margin-right: 8rpx;
}

.tag-close {
  font-size: 20rpx;
  opacity: 0.8;
}

/* 多选弹窗样式 */
.multi-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.modal-content {
  background: #fff;
  width: 100%;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.modal-body {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.model-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.model-item {
  background: #f5f5f5;
  color: #333;
  padding: 16rpx 24rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
  text-align: center;
  transition: all 0.3s;
}

.model-item.selected {
  background: #333;
  color: #fff;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.btn-clear {
  flex: 1;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 50rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
}

.btn-clear::after {
  border: none;
}

.btn-confirm {
  flex: 2;
  background: #333;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
}

.btn-confirm::after {
  border: none;
}

/* 内容区域 */
.content-area {
  flex: 1;
  margin: 20rpx 30rpx;
  background: #fff;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 600rpx;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
  display: flex;
  align-items: center;
  justify-content: center;
}

.document-icon {
  width: 120rpx;
  height: 150rpx;
  background: #f0f0f0;
  border-radius: 10rpx;
  position: relative;
  padding: 20rpx;
  box-sizing: border-box;
}

.doc-header {
  width: 60rpx;
  height: 20rpx;
  background: #ddd;
  border-radius: 4rpx;
  margin-bottom: 15rpx;
}

.doc-line {
  width: 100%;
  height: 8rpx;
  background: #e8e8e8;
  border-radius: 2rpx;
  margin-bottom: 8rpx;
}

.doc-line:last-child {
  width: 70%;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
}

/* 底部按钮 */
.bottom-buttons {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
}

.btn-back {
  flex: 1;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 50rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
}

.btn-back::after {
  border: none;
}

.btn-generate {
  flex: 2;
  background: #333;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
}

.btn-generate::after {
  border: none;
}

.btn-generate:active {
  background: #555;
}

.btn-back:active {
  background: #e0e0e0;
}
