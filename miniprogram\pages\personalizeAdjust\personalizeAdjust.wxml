<!--pages/personalizeAdjust/personalizeAdjust.wxml-->
<view class="container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <view class="nav-left" bindtap="goBack">
      <text class="back-icon">‹</text>
    </view>
    <view class="nav-title">个性化调整</view>
    <view class="nav-right">
      <text class="more-icon">⋯</text>
      <text class="record-icon">○</text>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="tabs-container">
    <view class="tabs">
      <view
        wx:for="{{tabs}}"
        wx:key="index"
        class="tab-item {{currentTab === index ? 'active' : ''}}"
        data-index="{{index}}"
        bindtap="switchTab"
      >
        {{item}}
      </view>
    </view>
  </view>

  <!-- 选择型号 -->
  <view class="model-selector">
    <view class="selector-label">选择型号：</view>
    <view class="selector-input" bindtap="showModelPicker">
      <text class="{{selectedModel ? 'selected' : 'placeholder'}}">
        {{selectedModel || modelPlaceholder}}
      </text>
      <text class="dropdown-icon">▼</text>
    </view>
  </view>

  <!-- 空状态展示 -->
  <view class="empty-state">
    <view class="empty-icon">
      <image src="/images/null.png" mode="aspectFit"></image>
    </view>
    <view class="empty-text">如无需个性化调整，点击生成即可</view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-buttons">
    <view class="btn-secondary" bindtap="goBack">返回</view>
    <view class="btn-primary" bindtap="generateQuote">生成报价单</view>
  </view>
</view>

<!-- 型号选择弹窗 -->
<view class="modal-overlay {{showModelPicker ? 'show' : ''}}" bindtap="hideModelPicker">
  <view class="modal-content" catchtap="">
    <view class="modal-header">
      <view class="modal-title">选择型号</view>
      <view class="modal-close" bindtap="hideModelPicker">×</view>
    </view>
    <view class="modal-body">
      <view
        wx:for="{{modelList}}"
        wx:key="index"
        class="model-item"
        data-model="{{item}}"
        bindtap="selectModel"
      >
        {{item}}
      </view>
    </view>
  </view>
</view>