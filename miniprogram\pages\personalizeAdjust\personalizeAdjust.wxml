<view class="container">
  <!-- 手机类型切换 -->
  <view class="type-tabs">
    <view
      class="type-tab {{currentTypeIndex === index ? 'active' : ''}}"
      wx:for="{{phoneTypes}}"
      wx:key="index"
      data-index="{{index}}"
      bindtap="switchPhoneType"
    >
      {{item}}
    </view>
  </view>

  <!-- 选择型号 -->
  <view class="model-selector">
    <text class="selector-label">选择型号：</text>
    <picker
      class="model-picker"
      bindchange="onModelChange"
      value="{{modelIndex}}"
      range="{{modelList}}"
    >
      <view class="picker-display">
        <text class="picker-text">{{selectedModel || '点击此处选择需要调整的型号'}}</text>
        <text class="picker-arrow">▼</text>
      </view>
    </picker>

    <!-- 多选按钮 -->
    <view class="multi-select-btn" bindtap="showMultiSelector">
      <text class="multi-select-text">多选型号</text>
    </view>

    <!-- 已选择的多个型号显示 -->
    <view class="selected-models" wx:if="{{selectedModels.length > 0}}">
      <text class="selected-label">已选择：</text>
      <view class="selected-tags">
        <view class="selected-tag" wx:for="{{selectedModels}}" wx:key="*this" data-model="{{item}}" bindtap="removeSelectedModel">
          <text class="tag-text">{{item}}</text>
          <text class="tag-close">×</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content-area">
    <view class="empty-content">
      <view class="empty-icon">
        <view class="document-icon">
          <view class="doc-header"></view>
          <view class="doc-line"></view>
          <view class="doc-line"></view>
          <view class="doc-line"></view>
        </view>
      </view>
      <text class="empty-text">如无需个性化调整，点击生成即可</text>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="bottom-buttons">
    <button class="btn-back" bindtap="goBack">返回</button>
    <button class="btn-generate" bindtap="generateQuote">生成报价单</button>
  </view>

  <!-- 多选弹窗 -->
  <view class="multi-modal" wx:if="{{showMultiModal}}" bindtap="hideMultiModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">选择型号</text>
        <text class="modal-close" bindtap="hideMultiModal">×</text>
      </view>
      <view class="modal-body">
        <view class="model-grid">
          <view
            class="model-item {{tempSelectedModels.indexOf(item) !== -1 ? 'selected' : ''}}"
            wx:for="{{modelList}}"
            wx:key="*this"
            data-model="{{item}}"
            bindtap="toggleTempModel"
          >
            {{item}}
          </view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="btn-clear" bindtap="clearTempSelection">清空已选</button>
        <button class="btn-confirm" bindtap="confirmMultiSelection">确认已选型号</button>
      </view>
    </view>
  </view>
</view>
