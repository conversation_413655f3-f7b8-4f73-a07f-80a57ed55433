<view class="container">
  <!-- 账号类型选择 -->
  <view class="form-item">
    <text class="label">账号类型</text>
    <radio-group class="radio-group" bindchange="handleAccountTypeChange">
      <label class="radio-label">
        <radio value="bank" checked="{{accountType === 'bank'}}" color="#007aff" /> 银行卡
      </label>
      <label class="radio-label" wx:if="{{show}}">
        <radio value="alipay" checked="{{accountType === 'alipay'}}" color="#007aff" /> 支付宝
      </label>
      <label class="radio-label">
        <radio value="wechat" checked="{{accountType === 'wechat'}}" color="#007aff" /> 微信收款码
      </label>
    </radio-group>
  </view>

 <!-- 银行名称 -->
 <view class="form-item" wx:if="{{accountType == 'bank'}}">
    <text class="label">银行名称</text>
    <input 
      class="input" 
      placeholder="请输入银行名称" 
      placeholder-class="placeholder"
      bindinput="handleAccountInput"
      value="{{bankName}}"
    />
  </view>

  <!-- 收款人姓名 -->
  <view class="form-item">
    <text class="label">{{accountType === 'wechat'?'收款人姓名':'收款户名（户主真实姓名）'}}</text>
    <input 
      class="input" 
      placeholder="{{accountType === 'wechat'?'请输入收款人姓名':'请输入户主真实姓名'}}" 
      placeholder-class="placeholder"
      bindinput="handleNameInput"
      value="{{accountName}}"
    />
  </view>

  <!-- 手机号/收款账号 -->
  <view class="form-item">
    <text class="label">{{accountType === 'wechat'?'手机号':'收款账号'}}</text>
    <input 
      class="input" 
      type="number" 
      placeholder="请输入手机号" 
      placeholder-class="placeholder"
      bindinput="handlePhoneInput"
      value="{{accountNumber}}"
    />
  </view>

  <!-- 微信收款码上传 -->
  <view class="form-item" wx:if="{{accountType === 'wechat'}}">
    <text class="label">微信收款码</text>
    <view class="upload-area">
      <block wx:if="{{!wechatQRCode}}">
        <view class="upload-placeholder" bindtap="chooseImage">
          <text class="upload-text">+</text>
        </view>
      </block>
      <block wx:else>
        <image src="{{wechatQRCode}}" mode="aspectFill" class="qr-code-image"></image>
        <icon type="clear" size="20" color="#ff4d4f" class="delete-icon" catchtap="removeImage"></icon>
      </block>
    </view>
  </view>

  <!-- 设为默认 -->
  <view class="form-item switch-item">
    <text class="label">设为默认收款方式</text>
    <switch checked="{{isDefault}}" bindchange="handleDefaultChange" color="#007aff" />
  </view>

  <!-- 协议同意 -->
  <view class="agreement">
    <view class="agreement-label">
      <radio checked="{{agreed}}" color="#007aff" bindtap="toggleAgreement" />
      <text>已阅读并同意</text>
      <text class="agreement-link" catchtap="navigateToAgreement">《用户协议与隐私协议》</text>
    </view>
  </view>

  <!-- 保存按钮 -->
  <view class="{{agreed?'submit-btn':'submit-btn-disabled'}}" bindtap="handleSubmit">保存</view>
</view>