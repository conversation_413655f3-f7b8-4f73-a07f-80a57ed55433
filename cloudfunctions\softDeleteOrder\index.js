// 云函数入口文件
const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  const db = cloud.database()
  const _ = db.command
  const wxContext = cloud.getWXContext()
  
  // 参数验证
  if (!event._id) {
    return {
      code: 400,
      message: '缺少必要参数: _id'
    }
  }

  try {
    // 先查询订单是否存在且属于当前用户
    const order = await db.collection('orders')
      .where({
        _id: event._id,
        _openid: wxContext.OPENID // 确保只能修改自己的订单
      })
      .get()

    if (order.data.length === 0) {
      return {
        code: 404,
        message: '未找到指定订单或无权操作'
      }
    }

    // 执行更新操作 - 设置isDelete为true并更新updatedTime
    const result = await db.collection('orders').doc(event._id).update({
      data: {
        isDelete: true,
        updatedTime: new Date() // 更新修改时间
      }
    })

    return {
      code: 200,
      message: '订单已标记为删除',
      data: {
        updatedId: event._id,
        updatedFields: {
          isDelete: true,
          updatedTime: new Date()
        }
      }
    }
  } catch (err) {
    console.error('更新失败:', err)
    return {
      code: 500,
      message: '更新失败',
      error: err.message
    }
  }
}