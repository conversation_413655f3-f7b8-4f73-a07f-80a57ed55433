<!--pages/qutote/qutote.wxml-->
<view class="box2">
  <view class="tab-container">
    <view class="tab-item" bindtap="goToQuoteDetail">
      回收报价
    </view>
  </view>
  <!-- 品牌展示容器 -->
  <view class="brand-container">
    <!-- 循环渲染品牌列表 -->
    <block wx:for="{{androidBrandList}}" wx:key="index">
      <view class="brand-item" bindtap="previewImage" data-src="{{item.watermark_photo}}"  data-index="{{index}}">
        <!-- 品牌图片 -->
        <image class="brand-image" src="{{item.image}}" mode="aspectFit"></image>
        <!-- 品牌名称 -->
        <view class="brand-name">{{item.name}}</view>
      </view>
    </block>
  </view>
  <view style="height: 50rpx;width: 100%;"></view>
</view>

<!-- 自定义预览容器 -->
<scroll-view scroll-y style="height: 100vh;" wx:if="{{isPreviewing}}" class="preview-container">
  <!-- 关闭按钮 -->
  <view class="close-btn" bindtap="closePreview">×</view>
  
  <!-- 可缩放图片区域 -->
      <image 
        src="{{currentImage}}" 
        mode="widthFix" 
        style="width: 100%;" 
      />
  
  <view style="width: 100%;height: 100rpx;"></view>

  <!-- 底部信息栏（固定在底部） -->
  <view class="image-info" style="position: fixed; bottom: 0;">
    浏览量: {{viewCount}} | 更新时间: {{updateTime}}
  </view>
</scroll-view>