.container {
  padding: 30rpx;
  background-color: #f7f7f7;
  min-height: 100vh;
  border-radius: 12rpx;
}

.form-item {
  background-color: #fff;
  padding: 30rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  font-size: 28rpx;
  border-bottom: 1rpx solid #eee;
  padding: 10rpx 0;
}

.placeholder {
  color: #ccc;
}

.radio-group {
  display: flex;
}

.radio-label {
  display: flex;
  align-items: center;
  height: 80rpx;
  font-size: 28rpx;
  width: 200rpx;
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.agreement {
  margin: 40rpx 0;
  padding: 0 30rpx;
  font-size: 26rpx;
  color: #666;
}

.agreement-label {
  display: flex;
  align-items: center;
}

.agreement-link {
  color: #576b95;
  margin-left: 5rpx;
}

.submit-btn {
  margin-top: 30rpx;
  background-color: #007aff;
  color: #fff;
  border-radius: 12rpx;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 32rpx;
  width: 100%;
  text-align: center;
}

.submit-btn-disabled {
  margin-top: 30rpx;
  background-color: #469efddc;
  color: #fff;
  border-radius: 12rpx;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 32rpx;
  width: 100%;
  text-align: center;
}

/* 微信收款码上传区域 */
.upload-area {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-top: 20rpx;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
  width: 100%;
}

.upload-text {
  font-size: 120rpx;
}

.qr-code-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-icon {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background: #fff;
  border-radius: 50%;
  padding: 4rpx;
  box-shadow: 0 0 8rpx rgba(0,0,0,0.2);
  z-index: 2;
}