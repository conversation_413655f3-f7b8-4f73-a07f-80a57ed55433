<view class="container">
  <view class="header">
    <text class="title">欢迎登录</text>
    <text class="subtitle">请输入您的手机号码</text>
  </view>

  <view class="input-container">
    <view class="input-group">
      <text class="prefix">+86</text>
      <input type="number" placeholder="请输入手机号码" placeholder-class="placeholder" maxlength="11" bindinput="handlePhoneInput" value="{{phoneNumber}}" class="input" />
    </view>
    <view class="underline"></view>
  </view>

  <button class="login-btn {{isPhoneValid && agreeProtocol ? 'active' : ''}}" bindtap="handleLogin" disabled="{{!isPhoneValid || !agreeProtocol}}">
    快速登录
  </button>

  <!-- 新增：同意协议勾选框 -->
  <view class="agreement-check">
    <checkbox-group bindchange="handleAgreeChange" style="margin-left: 25rpx;">
      <checkbox value="agree" checked="{{agreeProtocol}}" color="#07c160" />
    </checkbox-group>
    <text class="agreement-text">我已阅读并同意<text class="link" bindtap="goToProtocol">《用户服务协议》</text>和<text class="link" bindtap="goToProtocol">《隐私政策》</text>
    </text>
  </view>
</view>