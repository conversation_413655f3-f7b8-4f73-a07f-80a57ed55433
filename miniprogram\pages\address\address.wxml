<!--pages/address/address.wxml-->
<view style="width: 90%;margin: 40% auto;text-align: center;" wx:if="{{ !loading && address.length == 0}}">
  <image src="../../images/null.png" mode="widthFix" style="width: 80%;" />
  <view style="color: #515151;margin-top: 50rpx;font-size: 30rpx;">没有更多地址了，点击下放新增地址</view>
</view>
<view style="text-align: center;font-size: 25rpx;color: #666;margin: 40rpx 0;" wx:if="{{loading}}">加载中...</view>
<view wx:if="{{address.length > 0}}">
  <view class="address-card default-card" bind:tap="editAddressBook" data-id="{{item._id}}" wx:for="{{address}}" wx:key="_id">
    <!-- 左侧内容 -->
    <view class="card-left">
      <!-- 默认标签 -->
      <view class="default-tag" wx:if="{{item.is_default}}">默认</view>

      <!-- 收货信息 -->
      <view class="info-section">
        <view class="name-phone">
          <text class="name">{{item.consignee}}</text>
          <text class="phone">{{item.phone}}</text>
        </view>
        <view class="address">
          <text class="region">{{item.province}}，{{item.city}}，{{item.district}}</text>
          <text class="detail">{{item.detail}}</text>
        </view>
      </view>
    </view>
    <!-- 编辑按钮 -->
    <view class="edit-btn" bindtap="onEdit">
      <image src="../../images/right.png" mode="aspectFit"></image>
    </view>
  </view>
  <view style="text-align: center;font-size: 25rpx;color: #666;margin: 40rpx 0;">没有更多了</view>
</view>
<view class="btn-bottom" bind:tap="goToAddAddress">+ 新增收货地址</view>