/* pages/order/order.wxss */
page {
  background-color: #f6f7f9;
}

.container {
  padding: 20rpx;
}

/* 搜索区域 */
.search-container {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  padding: 0 20rpx;
}

.placeholder-style {
  color: #999;
}

.search-btn {
  width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 28rpx;
  color: #fff;
  background-color: #5677fc;
  margin-left: 20rpx;
  border-radius: 10rpx;
  text-align: center;
}

/* 状态选择器 */
.status-container {
  background: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.status-scroll {
  white-space: nowrap;
  width: 100%;
  height: 80rpx;
}

/* 隐藏滚动条 */
.status-scroll ::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.status-item {
  display: inline-block;
  padding: 0 30rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.status-item.active {
  color: #5677fc;
  font-weight: bold;
}

.status-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #5677fc;
}

/* pages/order/list.wxss */
.page-container {
  width: 100%;
}

.order-scroll {
  height: 78vh;
  margin: 0 auto;
  width: 95%;
}

.order-scroll ::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.order-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.order-id {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-body {
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 26rpx;
}

.info-label {
  color: #666;
  width: 140rpx;
}

.info-value {
  color: #333;
  flex: 1;
}

.image-container {
  margin: 20rpx 0;
}

.remark-image {
  width: 140rpx;
  height: 140rpx;
  border-radius: 8rpx;
}

.order-footer {
  display: flex;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  justify-content: space-between;
}

.delete-btn {
  background-color: #e02323;
  color: #fff;
  border-radius: 8rpx;
  font-size: 26rpx;
  padding: 0 30rpx;
  height: 60rpx;
  line-height: 60rpx;
}

.comfirm-btn {
  background-color: #409EFF;
  color: #fff;
  border-radius: 8rpx;
  font-size: 26rpx;
  padding: 0 30rpx;
  height: 60rpx;
  line-height: 60rpx;
}

.recall-btn {
  background-color: #fef0f0;
  color: #f56c6c;
  border-radius: 8rpx;
  font-size: 26rpx;
  padding: 0 30rpx;
  height: 60rpx;
  line-height: 60rpx;
  border: 1rpx solid #f56c6c;
}

.loading-text {
  text-align: center;
  font-size: 26rpx;
  color: #999;
  padding: 20rpx 0;
}