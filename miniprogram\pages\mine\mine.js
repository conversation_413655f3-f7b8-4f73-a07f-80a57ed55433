// pages/mine/mine.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isReceived: false,
    phone: '',
    showModal: false,
    orderCount: 0,
    totalAmount: 0
  },
  showModal() {
    this.setData({
      showModal: true
    });
  },
  hideModal() {
    this.setData({
      showModal: false
    });
  },
  selectAddress() {
    wx.navigateTo({
      url: '../address/address?type=1',
    })
  },
  selectReturn() {
    wx.navigateTo({
      url: '../address/address?type=2',
    })
  },

  // 跳转报价页面
  goToQutote() {
    wx.navigateTo({
      url: '../qutote/qutote',
    })
  },

  contactCustomerService() {
    // 这里不需要实际逻辑，点击事件会由覆盖的button处理
  },

  // 跳转证件更新页面
  goToIdCard() {
    wx.navigateTo({
      url: '../updateIdCard/updateIdCard',
    })
  },

  // 跳转到收款页面
  goToCredit() {
    wx.navigateTo({
      url: '../creditList/creditList',
    })
  },

  // 跳转到交易须知页面
  goToMustKnow() {
    wx.navigateTo({
      url: '../mustKnow/mustKnow',
    })
  },

  // 跳转登录页面
  goToLogin() {
    wx.navigateTo({
      url: '../login/login',
    })
  },

  // 跳转购买订单页面
  goToBuy(e) {
    const status = e.currentTarget.dataset.status;
    wx.navigateTo({
      url: `../buyOrder/buyOrder?status=${status}`,
    })
  },

  // 跳转订单页面
  goToOrder(e) {
    const status = e.currentTarget.dataset.status;
    wx.navigateTo({
      url: `../recycleOrder/recycleOrder?status=${status}`,
    })
  },

  /**
   * 点击领取会员
   */
  handleReceive: function () {
    if (!this.data.isReceived) {
      // 设置为已领取状态
      this.setData({
        isReceived: true
      });

      // 存储到本地
      wx.setStorage({
        key: 'isReceived',
        data: true
      });

      // 可以在这里添加领取成功的提示
      wx.showToast({
        title: '领取成功',
        icon: 'success',
        duration: 1500
      });
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 从本地存储读取领取状态
    this.setData({
      isReceived: wx.getStorageSync('isReceived')
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    if (wx.getStorageSync('phone')) {
      wx.cloud.callFunction({
        name: 'getOrderStats',
        success: res => {
          this.setData({
            orderCount: res.result.data.orderCount,
            totalAmount: res.result.data.totalAmount,
            phone: wx.getStorageSync('phone')
          })
        },
        fail: err => {
          console.error('获取订单统计失败:', err)
        }
      })
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})