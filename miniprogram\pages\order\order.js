// pages/order/order.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    info: {
      name: '慕非',
      phone: '15323818868',
      address: '深圳市福田区锦峰大厦A座18F'
    },
    logisticsCompanies: [{
        id: 1,
        name: '京东快递'
      },
      {
        id: 2,
        name: '顺丰速运'
      },
      {
        id: 3,
        name: '中通快递'
      },
      {
        id: 4,
        name: '圆通速递'
      },
      {
        id: 5,
        name: '韵达快递'
      }
    ],
    selectedCompany: '',
    waybillNumber: '',
    quantity: '',
    remark: '',
    tempFilePaths: '',
    creditInfo: null,
    imageFileID: ''
  },

  // 点击选择收款方式
  goToCreditList() {
    wx.navigateTo({
      url: '../creditList/creditList?back=1',
    })
  },

  // 选择物流公司
  bindLogisticsCompanyChange(e) {
    const index = e.detail.value
    this.setData({
      selectedCompany: this.data.logisticsCompanies[index].name
    })
  },

  // 输入物流单号
  bindWaybillInput(e) {
    this.setData({
      waybillNumber: e.detail.value
    })
  },

  // 输入货物数量
  bindQuantityInput(e) {
    this.setData({
      quantity: e.detail.value
    })
  },

  // 输入备注信息
  bindRemarkInput(e) {
    this.setData({
      remark: e.detail.value
    })
  },

  // 选择图片
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempPath = res.tempFilePaths[0];
        this.setData({
          tempFilePaths: tempPath
        })
        // 生成时间戳文件名 + 后缀
        const ext = tempPath.match(/\.\w+$/)[0];
        const cloudPath = `images/${Date.now()}${ext}`;

        // 上传到云存储
        wx.cloud.uploadFile({
          cloudPath,
          filePath: tempPath
        }).then(uploadRes => {
          // 获取永久可访问的URL
          return wx.cloud.getTempFileURL({
            fileList: [uploadRes.fileID]
          });
        }).then(urlRes => {
          // 存储永久URL和FileID
          this.setData({
            imageFileID: urlRes.fileList[0].tempFileURL
          });
        }).catch(err => {
          console.error('upload error', err);
        });
      }
    })
  },

  // 预览图片
  previewImage(e) {
    wx.previewImage({
      current: e.currentTarget.dataset.src,
      urls: [this.data.tempFilePaths]
    })
  },

  // 删除图片
  removeImage() {
    this.setData({
      tempFilePaths: '',
      imageFileID: ''
    })
  },

  // 提交表单
  submitForm() {
    const {
      selectedCompany,
      waybillNumber,
      quantity,
      remark,
      creditInfo,
      imageFileID
    } = this.data

    if (!creditInfo) {
      wx.showToast({
        title: '请选择收款方式',
        icon: 'none'
      })
      return
    }

    if (!selectedCompany) {
      wx.showToast({
        title: '请选择物流公司',
        icon: 'none'
      })
      return
    }

    if (!waybillNumber) {
      wx.showToast({
        title: '请输入物流单号',
        icon: 'none'
      })
      return
    }

    if (!quantity || !Number.isInteger(parseInt(quantity)) || parseInt(quantity) < 1) {
      wx.showToast({
        title: '请输入有效的货物数量',
        icon: 'none'
      })
      return
    }

    // 然后提交表单数据
    wx.showLoading({
      title: '提交中...',
    })
    wx.cloud.callFunction({
      name: 'addOrder',
      data: {
        creditId: creditInfo._id,
        imageFileID,
        quantity,
        remark,
        selectedCompany,
        waybillNumber
      },
      success: res => {
        wx.hideLoading()
        if (res.result.code === 200) {
          wx.showToast({
            title: '订单创建成功',
            icon: 'success'
          })
          wx.redirectTo({
            url: '../recycleOrder/recycleOrder',
          })
        } else {
          wx.showToast({
            title: res.result.message,
            icon: 'none'
          })
        }
      },
      fail: err => {
        console.error('订单创建失败:', err)
        wx.showToast({
          title: '订单创建失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    wx.showLoading({
      title: '加载中...',
    })
    wx.cloud.callFunction({
      name: 'getCreditByDefault',
      success: res => {
        if (res.result.code === 200) {
          wx.hideLoading();
          // 处理默认账户数据
          this.setData({
            creditInfo: res.result.data
          })
        } else {
          wx.showToast({
            title: res.result.message,
            icon: 'none'
          })
        }
      },
      fail: err => {
        console.error('查询失败:', err)
        wx.showToast({
          title: '查询默认账户失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})