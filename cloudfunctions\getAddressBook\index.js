// 云函数入口文件
const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const db = cloud.database()
  
  // 解构参数
  const {
    type,       // 可选，按类型筛选(1或2)
  } = event

  try {
    // 构建查询条件
    const queryCondition = {
      openid: wxContext.OPENID
    }

    // 添加类型筛选条件
    if (type && (type === '1' || type === '2')) {
      queryCondition.type = type
    }

    // 执行查询
    const result = await db.collection('address_book')
      .where(queryCondition)
      .orderBy('createTime', 'desc')
      .get()

    return {
      code: 200,
      message: '获取成功',
      data: result.data
    }
  } catch (err) {
    console.error(err)
    return {
      code: 500,
      message: '获取地址失败',
      error: err
    }
  }
}