﻿Page({
  data: {
    currentTypeIndex: 0,
    phoneTypes: ['靓机/小花', '花机/内爆', '卡贴外版', '外版无锁'],
    modelIndex: 0,
    modelList: [
      'iPhone 16', 'iPhone 16 Pro', 'iPhone 16 Pro Max', 'iPhone 16 Plus',
      'iPhone 15', 'iPhone 15 Plus', 'iPhone 15 Pro', 'iPhone 15 Pro Max',
      'iPhone 14', 'iPhone 14 Plus', 'iPhone 14 Pro', 'iPhone 14 Pro Max',
      'iPhone 13 mini', 'iPhone 13', 'iPhone 13 Pro', 'iPhone 13 Pro Max',
      'iPhone 12 mini', 'iPhone 12', 'iPhone 12 Pro', 'iPhone 12 Pro Max',
      'iPhone 11', 'iPhone 11 Pro', 'iPhone 11 Pro Max'
    ],
    selectedModel: '',
    selectedModels: [],
    tempSelectedModels: [],
    showMultiModal: false
  },

  onLoad() {
  },

  switchPhoneType(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentTypeIndex: index
    });
  },

  onModelChange(e) {
    const index = e.detail.value;
    this.setData({
      modelIndex: index,
      selectedModel: this.data.modelList[index]
    });
  },

  // 显示多选弹窗
  showMultiSelector() {
    this.setData({
      showMultiModal: true,
      tempSelectedModels: [...this.data.selectedModels]
    });
  },

  // 隐藏多选弹窗
  hideMultiModal() {
    this.setData({
      showMultiModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
  },

  // 切换临时选择状态
  toggleTempModel(e) {
    const model = e.currentTarget.dataset.model;
    let tempSelected = [...this.data.tempSelectedModels];

    const index = tempSelected.indexOf(model);
    if (index > -1) {
      tempSelected.splice(index, 1);
    } else {
      tempSelected.push(model);
    }

    this.setData({
      tempSelectedModels: tempSelected
    });
  },

  // 清空临时选择
  clearTempSelection() {
    this.setData({
      tempSelectedModels: []
    });
  },

  // 确认多选
  confirmMultiSelection() {
    this.setData({
      selectedModels: [...this.data.tempSelectedModels],
      showMultiModal: false
    });
  },

  // 移除已选择的型号
  removeSelectedModel(e) {
    const model = e.currentTarget.dataset.model;
    let selectedModels = this.data.selectedModels.filter(item => item !== model);
    this.setData({
      selectedModels: selectedModels
    });
  },

  goBack() {
    wx.navigateBack();
  },

  generateQuote() {
    // 生成报价单逻辑
    wx.showToast({
      title: '正在生成报价单...',
      icon: 'loading',
      duration: 2000
    });

    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/quote/quote'
      });
    }, 2000);
  }
});
