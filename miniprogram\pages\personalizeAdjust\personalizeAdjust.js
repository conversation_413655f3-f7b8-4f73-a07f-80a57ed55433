// pages/personalizeAdjust/personalizeAdjust.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    currentTab: 0, // 当前选中的标签页
    tabs: ['靓机/小花', '死机/内爆', '卡贴外版', '外版'],
    selectedModel: '', // 选择的型号
    modelPlaceholder: '点击此处选择需要调整的型号',
    showModelPicker: false,
    modelList: [
      'iPhone 14 Pro Max',
      'iPhone 14 Pro',
      'iPhone 14 Plus',
      'iPhone 14',
      'iPhone 13 Pro Max',
      'iPhone 13 Pro',
      'iPhone 13',
      'iPhone 12 Pro Max',
      'iPhone 12 Pro',
      'iPhone 12'
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 切换标签页
   */
  switchTab(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentTab: index
    });
  },

  /**
   * 显示型号选择器
   */
  showModelPicker() {
    this.setData({
      showModelPicker: true
    });
  },

  /**
   * 隐藏型号选择器
   */
  hideModelPicker() {
    this.setData({
      showModelPicker: false
    });
  },

  /**
   * 选择型号
   */
  selectModel(e) {
    const model = e.currentTarget.dataset.model;
    this.setData({
      selectedModel: model,
      showModelPicker: false
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 生成报价单
   */
  generateQuote() {
    if (!this.data.selectedModel) {
      wx.showToast({
        title: '请先选择型号',
        icon: 'none'
      });
      return;
    }

    // 这里可以添加生成报价单的逻辑
    wx.showToast({
      title: '正在生成报价单...',
      icon: 'loading'
    });

    // 模拟跳转到报价详情页
    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/quoteDetail/quoteDetail'
      });
    }, 1500);
  }
})