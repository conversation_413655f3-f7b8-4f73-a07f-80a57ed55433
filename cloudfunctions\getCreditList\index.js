// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database();
// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const {OPENID} = wxContext;
  
  try {
    // 查询该用户的所有收款账户，按更新时间倒序排列
    const result = await db.collection('credit')
      .where({
        _openid: OPENID
      })
      .orderBy('createTime', 'desc')
      .get()
    
    return {
      code: 200,
      message: '查询成功',
      data: result.data
    }
  } catch (err) {
    console.error('查询失败:', err)
    return {
      code: 500,
      message: '服务器错误',
      error: err
    }
  }
}