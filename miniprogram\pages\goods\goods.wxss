/* pages/goods/goods.wxss */
page {
  background-color: #efefef;
}

.box {
  width: 92%;
  background-color: #fff;
  position: relative;
  margin: -80rpx auto 0;
  border-radius: 20rpx;
  display: flex;
  padding: 20rpx 10rpx;
}

.container {
  padding: 15rpx;
  box-sizing: border-box;
}

.goods-list {
  display: flex;
  flex-wrap: wrap;
}

.goods-item {
  width: 30%;
  margin: 10rpx;
  background: #fff;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

.goods-image {
  width: 100%;
  height: 200rpx;
  display: block;
}

.goods-info {
  padding: 15rpx;
  text-align: center;
}

.goods-name {
  font-size: 26rpx;
  color: #333;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.goods-price {
  font-size: 28rpx;
  color: #e64340;
  font-weight: bold;
  display: block;
  margin-top: 10rpx;
}

.no-more {
  width: 100%;
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 26rpx;
  margin-bottom: 150rpx;
}