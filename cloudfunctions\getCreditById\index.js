// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { OPENID } = wxContext
  const { _id } = event // 从event中获取要查询的_id

  // 参数验证
  if (!_id) {
    return {
      code: 400,
      message: '缺少必要参数_id'
    }
  }

  try {
    // 查询指定_id的记录，同时验证_openid确保只能查询自己的数据
    const result = await db.collection('credit')
      .where({
        _id,
        _openid: OPENID // 确保只能查询自己的数据
      })
      .get()

    // 检查是否找到记录
    if (result.data.length === 0) {
      return {
        code: 404,
        message: '未找到指定的收款账户'
      }
    }

    // 返回找到的记录
    return {
      code: 200,
      message: '查询成功',
      data: result.data[0] // 返回第一条记录
    }
  } catch (err) {
    console.error('查询失败:', err)
    return {
      code: 500,
      message: '服务器错误',
      error: err
    }
  }
}