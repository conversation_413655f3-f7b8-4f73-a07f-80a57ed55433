// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({env: cloud.DYNAMIC_CURRENT_ENV})

// 数据库引用
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    // 检查是否提供了id参数
    if (!event.id) {
      return {
        code: -1,
        message: '缺少id参数'
      }
    }
    
    // 更新views字段，使其自增1
    const result = await db.collection('recall_qutote')
      .doc(event.id)
      .update({
        data: {
          views: _.inc(1) // 使用数据库自增操作，确保并发安全
        }
      })
      
    // 检查是否更新成功
    if (result.stats.updated === 1) {
      return {
        code: 0,
        message: 'views更新成功',
        data: result
      }
    } else {
      return {
        code: 200,
        message: '未找到对应id的文档或无需更新',
        data: result
      }
    }
  } catch (err) {
    console.error('更新views失败：', err)
    return {
      code: 500,
      message: '更新失败',
      error: err.message
    }
  }
}
