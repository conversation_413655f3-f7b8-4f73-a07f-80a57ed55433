/* pages/creditList/creditList.wxss */
page {
  background-color: #f6f7f9;
}

.btn-bottom {
  width: 92%;
  height: 90rpx;
  background-color: #007aff;
  color: white;
  font-size: 32rpx;
  justify-content: center;
  display: flex;
  align-items: center;
  border-radius: 12rpx;
  position: fixed;
  left: 4%;
  right: 0;
  bottom: 12rpx;
  z-index: 10;
}

/* 账号卡片通用样式 */
.account-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 不同类型账号的左边框 */
.alipay {
  border-left: 8rpx solid #00a0e9;
}

.bank {
  border-left: 8rpx solid #f5a623;
}

.wechat {
  border-left: 8rpx solid #07c160;
}

.special {
  border-left: 8rpx solid #eb3e2d;
}

.account-list {
  width: 92%;
  margin: 20rpx auto;
}

.account-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
}

.account-type {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.default-tag {
  background-color: #5677fc;
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  margin-left: 20rpx;
}

/* 账号详情 */
.account-detail {
  padding-left: 20rpx;
}

.detail-item {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.qr-code {
  width: 200rpx;
  height: 200rpx;
  margin-top: 20rpx;
  border: 1rpx solid #eee;
}

/* 新增编辑按钮样式 */
.account-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  justify-content: space-between; /* 新增 */
  padding-left: 20rpx;
}

.account-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.edit-btn {
  display: flex;
  align-items: center;
  color: #576b95;
  font-size: 24rpx;
  padding: 8rpx 10rpx;
  border-radius: 12rpx;
  border: 1rpx solid #576b95;
  margin-left: 45rpx;
}

/* 调整原有样式 */
.account-type {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  /* 移除flex: 1 */
}