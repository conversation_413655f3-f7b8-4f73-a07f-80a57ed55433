// 云函数入口文件
const cloud = require('wx-server-sdk')
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const db = cloud.database()
  const _ = db.command

  // 解构参数
  const { id } = event

  // 参数校验
  if (!id) {
    return {
      code: 400,
      message: '缺少ID参数'
    }
  }

  try {
    // 1. 先验证属于当前用户
    const checkResult = await db.collection('credit')
      .where({
        _id: id,
        _openid: wxContext.OPENID
      })
      .count()

    if (checkResult.total === 0) {
      return {
        code: 404,
        message: '无权操作'
      }
    }

    // 2.查询是否绑定订单信息
    const orderResult = await db.collection('orders')
      .where({
        creditId: id
      })
      .count()

      if (orderResult.total > 0) {
        return {
          code: 404,
          message: '已绑定订单，不能删除'
        }
      }

    // 3. 执行删除
    const deleteResult = await db.collection('credit')
      .doc(id)
      .remove()

    return {
      code: 200,
      message: '删除成功',
      data: deleteResult
    }
  } catch (err) {
    console.error(err)
    
    // 处理特定错误
    if (err.errCode === 'DATABASE_PERMISSION_DENIED') {
      return {
        code: 403,
        message: '无权操作'
      }
    }

    return {
      code: 500,
      message: '删除失败',
      error: err.message
    }
  }
}