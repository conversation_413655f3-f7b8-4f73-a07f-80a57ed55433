// pages/creditList/creditList.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    creditList: [],
    back: "0"
  },

  // 选择收款方式
  handleChoseCredit(e) {
    if(this.data.back=="0"){
      return
    }
    let creditInfo = this.data.creditList[e.currentTarget.dataset.index]
    // 当前页面（返回前调用上一页方法）
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2]; // 上一页实例
    if (prevPage) {
      prevPage.setData({
        creditInfo: creditInfo
      }); // 更新上一页数据
    }
    wx.navigateBack();
  },

  // 编辑按钮
  onEdit(e) {
    wx.navigateTo({
      url: `../editCredit/editCredit?id=${e.currentTarget.dataset.id}&type=${e.currentTarget.dataset.type}`,
    })
  },

  // 跳转新增页面
  goToAddCredit() {
    wx.navigateTo({
      url: '../addCredit/addCredit',
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if(options.back){
      this.setData({
        back: options.back
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    wx.showLoading({
      title: '加载中...',
      mask: true // 防止触摸穿透
    })
    const that = this; // 保存 this 引用
    wx.cloud.callFunction({
      name: 'getCreditList',
      success: res => {
        wx.hideLoading()
        that.setData({
          creditList: res.result.data
        })
      },
      fail: err => {
        console.error('查询失败:', err)
      }
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})